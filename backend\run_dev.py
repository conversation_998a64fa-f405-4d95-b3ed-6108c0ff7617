#!/usr/bin/env python3
"""
开发环境启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """检查环境要求"""
    print("🔍 检查环境要求...")

    # 检查Python版本
    if sys.version_info < (3, 9):
        print("❌ Python版本需要3.9或更高")
        return False

    print(f"✅ Python版本: {sys.version}")
    return True

def setup_environment():
    """设置环境"""
    print("🔧 设置环境...")
    
    # 创建.env文件（如果不存在）
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 创建.env文件...")
        env_file.write_text(env_example.read_text(encoding='utf-8'), encoding='utf-8')
        print("⚠️  请编辑.env文件配置数据库和其他设置")
    
    # 创建必要的目录
    directories = ["uploads", "logs"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 创建目录: {directory}")

def start_services():
    """启动服务"""
    print("🚀 启动开发服务器...")
    print("📍 服务地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("🔄 按 Ctrl+C 停止服务器")
    print("-" * 50)

    # 设置环境变量
    os.environ["PYTHONPATH"] = str(Path.cwd())

    try:
        # 启动FastAPI应用
        subprocess.run([
            "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False

    return True

def main():
    """主函数"""
    print("🎯 AI记账应用 - 开发环境启动")
    print("=" * 50)
    
    # 检查环境
    if not check_requirements():
        sys.exit(1)
    
    # 设置环境
    setup_environment()
    
    # 启动服务
    if not start_services():
        sys.exit(1)

if __name__ == "__main__":
    main()
