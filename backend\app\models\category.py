from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import json

from ..database.base import Base


class Category(Base):
    """分类模型"""
    __tablename__ = "categories"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    parent_id = Column(String(36), ForeignKey("categories.id"), nullable=True, index=True)
    
    # 分类详情
    name = Column(String(100), nullable=False)
    category_type = Column(String(20), nullable=False)  # income, expense
    icon = Column(String(50))  # 图标名称
    color = Column(String(7))  # 颜色代码 #RRGGBB
    description = Column(Text)
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)  # 是否为系统预设分类
    sort_order = Column(String(10), default="0")
    
    # 同步相关
    sync_id = Column(String(100), unique=True)
    last_sync = Column(DateTime(timezone=True))
    sync_status = Column(String(20), default="pending")  # pending, synced, conflict
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系（暂时注释掉）
    # user = relationship("User", back_populates="categories")
    # parent = relationship("Category", remote_side=[id], back_populates="children")
    # children = relationship("Category", back_populates="parent", cascade="all, delete-orphan")
    # transactions = relationship("Transaction", back_populates="category")

    def __repr__(self):
        return f"<Category(id={self.id}, name={self.name}, type={self.category_type})>"

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "parent_id": str(self.parent_id) if self.parent_id else None,
            "name": self.name,
            "category_type": self.category_type,
            "icon": self.icon,
            "color": self.color,
            "description": self.description,
            "is_active": self.is_active,
            "is_system": self.is_system,
            "sort_order": self.sort_order,
            "sync_id": self.sync_id,
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "sync_status": self.sync_status,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

    def deactivate(self):
        """停用分类"""
        self.is_active = False

    def activate(self):
        """激活分类"""
        self.is_active = True

    def is_parent_category(self) -> bool:
        """检查是否为父分类"""
        return self.parent_id is None

    def is_child_category(self) -> bool:
        """检查是否为子分类"""
        return self.parent_id is not None

    @classmethod
    def create_default_categories(cls, user_id: str):
        """为新用户创建默认分类"""
        # 支出分类
        expense_categories = [
            # 主分类
            {"name": "餐饮", "icon": "restaurant", "color": "#FF6B6B", "children": [
                {"name": "早餐", "icon": "breakfast", "color": "#FF8E8E"},
                {"name": "午餐", "icon": "lunch", "color": "#FF8E8E"},
                {"name": "晚餐", "icon": "dinner", "color": "#FF8E8E"},
                {"name": "零食", "icon": "snack", "color": "#FF8E8E"},
            ]},
            {"name": "交通", "icon": "transport", "color": "#4ECDC4", "children": [
                {"name": "公交", "icon": "bus", "color": "#6ED5D0"},
                {"name": "地铁", "icon": "subway", "color": "#6ED5D0"},
                {"name": "打车", "icon": "taxi", "color": "#6ED5D0"},
                {"name": "加油", "icon": "gas", "color": "#6ED5D0"},
            ]},
            {"name": "购物", "icon": "shopping", "color": "#45B7D1", "children": [
                {"name": "服装", "icon": "clothes", "color": "#6BC5E0"},
                {"name": "日用品", "icon": "daily", "color": "#6BC5E0"},
                {"name": "电子产品", "icon": "electronics", "color": "#6BC5E0"},
            ]},
            {"name": "娱乐", "icon": "entertainment", "color": "#96CEB4", "children": [
                {"name": "电影", "icon": "movie", "color": "#A8D5C1"},
                {"name": "游戏", "icon": "game", "color": "#A8D5C1"},
                {"name": "运动", "icon": "sport", "color": "#A8D5C1"},
            ]},
            {"name": "医疗", "icon": "medical", "color": "#FFEAA7", "children": [
                {"name": "看病", "icon": "doctor", "color": "#FFECB3"},
                {"name": "买药", "icon": "medicine", "color": "#FFECB3"},
            ]},
            {"name": "教育", "icon": "education", "color": "#DDA0DD", "children": [
                {"name": "学费", "icon": "tuition", "color": "#E6B3E6"},
                {"name": "书籍", "icon": "book", "color": "#E6B3E6"},
            ]},
            {"name": "住房", "icon": "housing", "color": "#F0A500", "children": [
                {"name": "房租", "icon": "rent", "color": "#F3B333"},
                {"name": "水电费", "icon": "utilities", "color": "#F3B333"},
            ]},
        ]
        
        # 收入分类
        income_categories = [
            {"name": "工资", "icon": "salary", "color": "#00B894"},
            {"name": "奖金", "icon": "bonus", "color": "#00CEC9"},
            {"name": "投资", "icon": "investment", "color": "#6C5CE7"},
            {"name": "兼职", "icon": "part_time", "color": "#A29BFE"},
            {"name": "其他收入", "icon": "other_income", "color": "#FD79A8"},
        ]
        
        categories = []
        
        # 创建支出分类
        for cat_data in expense_categories:
            parent_cat = cls(
                user_id=user_id,
                name=cat_data["name"],
                category_type="expense",
                icon=cat_data["icon"],
                color=cat_data["color"],
                is_system=True,
                sync_id=str(uuid.uuid4()),
            )
            categories.append(parent_cat)
            
            # 创建子分类
            if "children" in cat_data:
                for child_data in cat_data["children"]:
                    child_cat = cls(
                        user_id=user_id,
                        name=child_data["name"],
                        category_type="expense",
                        icon=child_data["icon"],
                        color=child_data["color"],
                        is_system=True,
                        sync_id=str(uuid.uuid4()),
                    )
                    # 注意：这里需要在保存后设置parent_id
                    categories.append(child_cat)
        
        # 创建收入分类
        for cat_data in income_categories:
            cat = cls(
                user_id=user_id,
                name=cat_data["name"],
                category_type="income",
                icon=cat_data["icon"],
                color=cat_data["color"],
                is_system=True,
                sync_id=str(uuid.uuid4()),
            )
            categories.append(cat)
        
        return categories
