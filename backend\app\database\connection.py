from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base
from sqlalchemy import MetaData
import redis.asyncio as redis
from typing import AsyncGenerator
from loguru import logger

from ..config import settings, DatabaseConfig

# 创建数据库引擎
database_url = DatabaseConfig.get_database_url()
if database_url.startswith("sqlite"):
    # SQLite不支持连接池配置
    engine = create_async_engine(
        database_url,
        echo=settings.DEBUG,
    )
else:
    # PostgreSQL支持连接池配置
    engine = create_async_engine(
        database_url,
        **DatabaseConfig.get_engine_config(),
        echo=settings.DEBUG,
    )

# 创建会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
)

# 导入基础模型类和所有模型
from .base import Base
from ..models.user import User, UserSession
from ..models.account import Account
from ..models.transaction import Transaction
from ..models.category import Category
from ..models.budget import Budget
from ..models.user_ai_config import UserAIConfig, AIUsageLog

# 元数据
metadata = MetaData()

# Redis连接
redis_client: redis.Redis = None


async def init_db():
    """初始化数据库连接"""
    global redis_client

    try:
        # 尝试初始化Redis连接
        try:
            redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )

            # 测试Redis连接
            await redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as redis_error:
            logger.warning(f"Redis连接失败，将在无缓存模式下运行: {redis_error}")
            redis_client = None

        # 创建数据库表（在生产环境中应该使用Alembic迁移）
        if settings.DEBUG:
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建成功")

    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


async def close_db():
    """关闭数据库连接"""
    global redis_client
    
    try:
        if redis_client:
            await redis_client.close()
            logger.info("Redis连接已关闭")
            
        await engine.dispose()
        logger.info("数据库引擎已关闭")
        
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话错误: {e}")
            raise
        finally:
            await session.close()


async def get_redis() -> redis.Redis:
    """获取Redis客户端"""
    global redis_client
    if redis_client is None:
        redis_client = redis.from_url(
            settings.REDIS_URL,
            encoding="utf-8",
            decode_responses=True
        )
    return redis_client


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = engine
        self.session_factory = AsyncSessionLocal
    
    async def create_session(self) -> AsyncSession:
        """创建新的数据库会话"""
        return self.session_factory()
    
    async def execute_query(self, query: str, params: dict = None):
        """执行原始SQL查询"""
        async with self.session_factory() as session:
            try:
                result = await session.execute(query, params or {})
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                logger.error(f"执行查询失败: {e}")
                raise
    
    async def health_check(self) -> bool:
        """数据库健康检查"""
        try:
            async with self.session_factory() as session:
                await session.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            return False


class RedisManager:
    """Redis管理器"""
    
    def __init__(self):
        self.client = None
    
    async def get_client(self) -> redis.Redis:
        """获取Redis客户端"""
        if self.client is None:
            self.client = await get_redis()
        return self.client
    
    async def set(self, key: str, value: str, expire: int = None):
        """设置缓存"""
        client = await self.get_client()
        await client.set(key, value, ex=expire)
    
    async def get(self, key: str) -> str:
        """获取缓存"""
        client = await self.get_client()
        return await client.get(key)
    
    async def delete(self, key: str):
        """删除缓存"""
        client = await self.get_client()
        await client.delete(key)
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        client = await self.get_client()
        return await client.exists(key)
    
    async def health_check(self) -> bool:
        """Redis健康检查"""
        try:
            client = await self.get_client()
            await client.ping()
            return True
        except Exception as e:
            logger.error(f"Redis健康检查失败: {e}")
            return False


# 创建全局管理器实例
db_manager = DatabaseManager()
redis_manager = RedisManager()
