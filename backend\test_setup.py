#!/usr/bin/env python3
"""
测试后端设置的简单脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_imports():
    """测试所有模块是否可以正常导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试配置模块
        from app.config import settings
        print("✅ 配置模块导入成功")
        
        # 测试数据库连接模块
        from app.database.connection import Base, get_db
        print("✅ 数据库连接模块导入成功")
        
        # 测试用户模型
        from app.models.user import User, UserSession
        print("✅ 用户模型导入成功")
        
        # 测试JWT处理器
        from app.auth.jwt_handler import jwt_handler, password_handler
        print("✅ JWT处理器导入成功")
        
        # 测试认证依赖
        from app.auth.dependencies import get_current_user
        print("✅ 认证依赖导入成功")
        
        # 测试API路由
        from app.api import auth, accounts, transactions, budgets, reports, ai
        print("✅ API路由模块导入成功")
        
        # 测试主应用
        from app.main import app
        print("✅ 主应用导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


async def test_jwt_functionality():
    """测试JWT功能"""
    print("\n🔐 测试JWT功能...")
    
    try:
        from app.auth.jwt_handler import jwt_handler, create_user_token_data
        
        # 测试数据
        user_data = {
            "sub": "test-user-id",
            "email": "<EMAIL>",
            "display_name": "Test User",
            "is_premium": False
        }
        
        # 创建令牌
        access_token = jwt_handler.create_access_token(user_data)
        refresh_token = jwt_handler.create_refresh_token(user_data)
        
        print("✅ 令牌创建成功")
        
        # 验证令牌
        access_payload = jwt_handler.verify_token(access_token)
        refresh_payload = jwt_handler.verify_token(refresh_token)
        
        if access_payload and refresh_payload:
            print("✅ 令牌验证成功")
            return True
        else:
            print("❌ 令牌验证失败")
            return False
            
    except Exception as e:
        print(f"❌ JWT测试失败: {e}")
        return False


async def test_password_functionality():
    """测试密码功能"""
    print("\n🔒 测试密码功能...")
    
    try:
        from app.auth.jwt_handler import password_handler
        
        # 测试密码
        test_password = "test_password_123"
        
        # 生成哈希
        hashed = password_handler.get_password_hash(test_password)
        print("✅ 密码哈希生成成功")
        
        # 验证密码
        is_valid = password_handler.verify_password(test_password, hashed)
        is_invalid = password_handler.verify_password("wrong_password", hashed)
        
        if is_valid and not is_invalid:
            print("✅ 密码验证功能正常")
            return True
        else:
            print("❌ 密码验证功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 密码测试失败: {e}")
        return False


async def test_configuration():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from app.config import settings
        
        # 检查关键配置
        required_settings = [
            'SECRET_KEY',
            'ALGORITHM',
            'ACCESS_TOKEN_EXPIRE_MINUTES',
            'DATABASE_URL',
            'REDIS_URL'
        ]
        
        missing_settings = []
        for setting in required_settings:
            if not hasattr(settings, setting):
                missing_settings.append(setting)
        
        if missing_settings:
            print(f"❌ 缺少配置: {missing_settings}")
            return False
        else:
            print("✅ 配置检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🎯 AI记账应用后端设置测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("JWT功能", test_jwt_functionality),
        ("密码功能", test_password_functionality),
        ("配置检查", test_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行测试: {test_name}")
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！后端设置正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
