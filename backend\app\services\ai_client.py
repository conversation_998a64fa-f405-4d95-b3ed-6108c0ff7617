import asyncio
import json
import httpx
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
from loguru import logger

from .ai_config import AIProvider, AIModelConfig


class BaseAIClient(ABC):
    """AI客户端基类"""
    
    def __init__(self, config: AIModelConfig):
        self.config = config
        self.client = httpx.AsyncClient(timeout=config.timeout)
    
    @abstractmethod
    async def chat_completion(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        """聊天完成接口"""
        pass
    
    @abstractmethod
    def format_messages(self, user_message: str, system_message: str = None) -> List[Dict[str, str]]:
        """格式化消息"""
        pass
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


class OpenAIClient(BaseAIClient):
    """OpenAI客户端"""
    
    def format_messages(self, user_message: str, system_message: str = None) -> List[Dict[str, str]]:
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": user_message})
        return messages
    
    async def chat_completion(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.config.model_name,
            "messages": messages,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature
        }
        
        try:
            response = await self.client.post(
                f"{self.config.api_base}/chat/completions",
                headers=headers,
                json=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {e}")
            raise


class AzureOpenAIClient(BaseAIClient):
    """Azure OpenAI客户端"""
    
    def format_messages(self, user_message: str, system_message: str = None) -> List[Dict[str, str]]:
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": user_message})
        return messages
    
    async def chat_completion(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        headers = {
            "api-key": self.config.api_key,
            "Content-Type": "application/json"
        }
        
        data = {
            "messages": messages,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature
        }
        
        url = f"{self.config.api_base}/openai/deployments/{self.config.model_name}/chat/completions"
        if self.config.api_version:
            url += f"?api-version={self.config.api_version}"
        
        try:
            response = await self.client.post(url, headers=headers, json=data)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Azure OpenAI API调用失败: {e}")
            raise


class AnthropicClient(BaseAIClient):
    """Anthropic Claude客户端"""
    
    def format_messages(self, user_message: str, system_message: str = None) -> List[Dict[str, str]]:
        # Claude使用不同的消息格式
        messages = [{"role": "user", "content": user_message}]
        return messages
    
    async def chat_completion(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        headers = {
            "x-api-key": self.config.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        
        # 提取系统消息和用户消息
        system_message = ""
        user_messages = []
        
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                user_messages.append(msg)
        
        data = {
            "model": self.config.model_name,
            "max_tokens": self.config.max_tokens,
            "temperature": self.config.temperature,
            "messages": user_messages
        }
        
        if system_message:
            data["system"] = system_message
        
        try:
            response = await self.client.post(
                f"{self.config.api_base}/messages",
                headers=headers,
                json=data
            )
            response.raise_for_status()
            result = response.json()
            
            # 转换为OpenAI格式的响应
            return {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": result["content"][0]["text"]
                    }
                }],
                "usage": result.get("usage", {})
            }
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {e}")
            raise


class BaiduQianfanClient(BaseAIClient):
    """百度千帆客户端"""
    
    def format_messages(self, user_message: str, system_message: str = None) -> List[Dict[str, str]]:
        messages = []
        if system_message:
            messages.append({"role": "user", "content": system_message})
            messages.append({"role": "assistant", "content": "好的，我明白了。"})
        messages.append({"role": "user", "content": user_message})
        return messages
    
    async def _get_access_token(self) -> str:
        """获取百度访问令牌"""
        url = "https://aip.baidubce.com/oauth/2.0/token"
        params = {
            "grant_type": "client_credentials",
            "client_id": self.config.api_key,
            "client_secret": self.config.extra_config.get("secret_key")
        }
        
        response = await self.client.post(url, params=params)
        response.raise_for_status()
        return response.json()["access_token"]
    
    async def chat_completion(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        access_token = await self._get_access_token()
        
        url = f"{self.config.api_base}/{self.config.model_name}"
        params = {"access_token": access_token}
        
        data = {
            "messages": messages,
            "temperature": self.config.temperature,
            "max_output_tokens": self.config.max_tokens
        }
        
        try:
            response = await self.client.post(url, params=params, json=data)
            response.raise_for_status()
            result = response.json()
            
            # 转换为OpenAI格式的响应
            return {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": result["result"]
                    }
                }],
                "usage": result.get("usage", {})
            }
        except Exception as e:
            logger.error(f"百度千帆API调用失败: {e}")
            raise


class CustomAIClient(BaseAIClient):
    """自定义AI客户端"""
    
    def format_messages(self, user_message: str, system_message: str = None) -> List[Dict[str, str]]:
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": user_message})
        return messages
    
    async def chat_completion(self, messages: List[Dict[str, str]]) -> Dict[str, Any]:
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        # 支持自定义请求格式
        request_format = self.config.extra_config.get("request_format", "openai")
        
        if request_format == "openai":
            data = {
                "model": self.config.model_name,
                "messages": messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature
            }
            endpoint = "/chat/completions"
        else:
            # 其他自定义格式
            data = self.config.extra_config.get("custom_data", {})
            data.update({
                "messages": messages,
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature
            })
            endpoint = self.config.extra_config.get("endpoint", "/chat/completions")
        
        try:
            response = await self.client.post(
                f"{self.config.api_base}{endpoint}",
                headers=headers,
                json=data
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"自定义AI API调用失败: {e}")
            raise


class AIClientFactory:
    """AI客户端工厂"""
    
    _client_classes = {
        AIProvider.OPENAI: OpenAIClient,
        AIProvider.AZURE_OPENAI: AzureOpenAIClient,
        AIProvider.ANTHROPIC: AnthropicClient,
        AIProvider.BAIDU_QIANFAN: BaiduQianfanClient,
        AIProvider.CUSTOM: CustomAIClient,
    }
    
    @classmethod
    def create_client(cls, config: AIModelConfig) -> BaseAIClient:
        """创建AI客户端"""
        client_class = cls._client_classes.get(config.provider)
        if not client_class:
            # 对于未实现的提供商，使用自定义客户端
            client_class = CustomAIClient
        
        return client_class(config)
    
    @classmethod
    def get_supported_providers(cls) -> List[AIProvider]:
        """获取支持的提供商列表"""
        return list(cls._client_classes.keys())
