import 'package:json_annotation/json_annotation.dart';
import '../constants/app_constants.dart';

part 'account.g.dart';

@JsonSerializable()
class Account {
  final String id;
  final String userId;
  final String name;
  final AccountType type;
  final double balance;
  final String currency;
  final String? description;
  final String? iconName;
  final int? color;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? syncId; // 用于数据同步

  const Account({
    required this.id,
    required this.userId,
    required this.name,
    required this.type,
    required this.balance,
    this.currency = 'CNY',
    this.description,
    this.iconName,
    this.color,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.syncId,
  });

  factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);
  Map<String, dynamic> toJson() => _$AccountToJson(this);

  // 从数据库记录创建Account对象
  factory Account.fromMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      name: map['name'] as String,
      type: AccountType.values[map['type'] as int],
      balance: (map['balance'] as num).toDouble(),
      currency: map['currency'] as String? ?? 'CNY',
      description: map['description'] as String?,
      iconName: map['icon_name'] as String?,
      color: map['color'] as int?,
      isActive: (map['is_active'] as int) == 1,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      syncId: map['sync_id'] as String?,
    );
  }

  // 转换为数据库记录
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'type': type.index,
      'balance': balance,
      'currency': currency,
      'description': description,
      'icon_name': iconName,
      'color': color,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'sync_id': syncId,
    };
  }

  Account copyWith({
    String? id,
    String? userId,
    String? name,
    AccountType? type,
    double? balance,
    String? currency,
    String? description,
    String? iconName,
    int? color,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? syncId,
  }) {
    return Account(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      type: type ?? this.type,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncId: syncId ?? this.syncId,
    );
  }

  // 获取账户类型显示名称
  String get typeDisplayName {
    switch (type) {
      case AccountType.cash:
        return '现金';
      case AccountType.bankCard:
        return '银行卡';
      case AccountType.creditCard:
        return '信用卡';
      case AccountType.alipay:
        return '支付宝';
      case AccountType.wechat:
        return '微信';
      case AccountType.other:
        return '其他';
    }
  }

  // 获取默认图标
  String get defaultIcon {
    switch (type) {
      case AccountType.cash:
        return 'account_balance_wallet';
      case AccountType.bankCard:
        return 'credit_card';
      case AccountType.creditCard:
        return 'credit_card';
      case AccountType.alipay:
        return 'payment';
      case AccountType.wechat:
        return 'chat';
      case AccountType.other:
        return 'account_balance';
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Account && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Account(id: $id, name: $name, type: $type, balance: $balance)';
  }
}
