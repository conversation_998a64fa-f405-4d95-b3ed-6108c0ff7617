from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import J<PERSON><PERSON><PERSON><PERSON>, jwt
from passlib.context import Crypt<PERSON>ontext
import uuid

from ..config import settings

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class JWTHandler:
    """JWT令牌处理器"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def create_access_token(self, data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
        """创建访问令牌"""
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access",
            "jti": str(uuid.uuid4())  # JWT ID
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: Dict[str, Any]) -> str:
        """创建刷新令牌"""
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh",
            "jti": str(uuid.uuid4())
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except JWTError:
            return None
    
    def decode_token(self, token: str) -> Optional[Dict[str, Any]]:
        """解码令牌（不验证过期时间）"""
        try:
            payload = jwt.decode(
                token, 
                self.secret_key, 
                algorithms=[self.algorithm],
                options={"verify_exp": False}
            )
            return payload
        except JWTError:
            return None
    
    def is_token_expired(self, token: str) -> bool:
        """检查令牌是否过期"""
        payload = self.decode_token(token)
        if not payload:
            return True
        
        exp = payload.get("exp")
        if not exp:
            return True
        
        return datetime.utcnow() > datetime.fromtimestamp(exp)
    
    def get_token_type(self, token: str) -> Optional[str]:
        """获取令牌类型"""
        payload = self.decode_token(token)
        if payload:
            return payload.get("type")
        return None
    
    def create_token_pair(self, user_data: Dict[str, Any]) -> Dict[str, str]:
        """创建令牌对（访问令牌和刷新令牌）"""
        access_token = self.create_access_token(user_data)
        refresh_token = self.create_refresh_token(user_data)
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer"
        }


class PasswordHandler:
    """密码处理器"""
    
    @staticmethod
    def verify_password(plain_password: str, hashed_password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(plain_password, hashed_password)
    
    @staticmethod
    def get_password_hash(password: str) -> str:
        """获取密码哈希"""
        return pwd_context.hash(password)
    
    @staticmethod
    def generate_reset_token() -> str:
        """生成密码重置令牌"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_verification_token() -> str:
        """生成邮箱验证令牌"""
        return str(uuid.uuid4())


class TokenBlacklist:
    """令牌黑名单管理"""
    
    def __init__(self, redis_client):
        self.redis_client = redis_client
        self.blacklist_prefix = "blacklist:"
    
    async def add_token(self, token: str, expires_at: datetime):
        """将令牌添加到黑名单"""
        key = f"{self.blacklist_prefix}{token}"
        # 计算过期时间（秒）
        expire_seconds = int((expires_at - datetime.utcnow()).total_seconds())
        if expire_seconds > 0:
            await self.redis_client.set(key, "1", ex=expire_seconds)
    
    async def is_token_blacklisted(self, token: str) -> bool:
        """检查令牌是否在黑名单中"""
        key = f"{self.blacklist_prefix}{token}"
        return await self.redis_client.exists(key)
    
    async def remove_token(self, token: str):
        """从黑名单中移除令牌"""
        key = f"{self.blacklist_prefix}{token}"
        await self.redis_client.delete(key)


# 创建全局实例
jwt_handler = JWTHandler()
password_handler = PasswordHandler()


def create_user_token_data(user) -> Dict[str, Any]:
    """创建用户令牌数据"""
    return {
        "sub": str(user.id),  # subject
        "email": user.email,
        "display_name": user.display_name,
        "is_premium": user.is_premium,
    }


def extract_user_id_from_token(token: str) -> Optional[str]:
    """从令牌中提取用户ID"""
    payload = jwt_handler.verify_token(token)
    if payload:
        return payload.get("sub")
    return None


def extract_email_from_token(token: str) -> Optional[str]:
    """从令牌中提取邮箱"""
    payload = jwt_handler.verify_token(token)
    if payload:
        return payload.get("email")
    return None
