from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
import uuid

from ..auth.jwt_handler import jwt_handler, password_handler, create_user_token_data
from ..database.connection import get_db
from ..models.user import User
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

router = APIRouter()
security = HTTPBearer()


# 请求模型
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    display_name: Optional[str] = None


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class TokenRefresh(BaseModel):
    refresh_token: str


class GoogleAuthRequest(BaseModel):
    id_token: str


# 响应模型
class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict

    class Config:
        from_attributes = True


class UserResponse(BaseModel):
    id: str
    email: str
    display_name: Optional[str]
    photo_url: Optional[str]
    is_email_verified: bool
    created_at: datetime


@router.post("/register")
async def register(user_data: UserRegister, db: AsyncSession = Depends(get_db)):
    """用户注册"""

    # 检查邮箱是否已存在
    result = await db.execute(select(User).where(User.email == user_data.email))
    existing_user = result.scalar_one_or_none()

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )

    # 创建新用户
    hashed_password = password_handler.get_password_hash(user_data.password)
    new_user = User(
        email=user_data.email,
        display_name=user_data.display_name,
        hashed_password=hashed_password,
        is_email_verified=False,  # 需要邮箱验证
    )

    db.add(new_user)
    await db.commit()
    await db.refresh(new_user)

    # 生成令牌
    token_data = create_user_token_data(new_user)
    tokens = jwt_handler.create_token_pair(token_data)

    return JSONResponse(content={
        "access_token": tokens["access_token"],
        "refresh_token": tokens["refresh_token"],
        "token_type": "bearer",
        "expires_in": jwt_handler.access_token_expire_minutes * 60,
        "user": new_user.to_dict()
    })


@router.post("/login")
async def login(user_data: UserLogin, db: AsyncSession = Depends(get_db)):
    """用户登录"""

    # 查找用户
    result = await db.execute(select(User).where(User.email == user_data.email))
    user = result.scalar_one_or_none()

    if not user or not user.can_login_with_password():
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误"
        )

    # 验证密码
    if not password_handler.verify_password(user_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误"
        )

    # 检查用户状态
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="账户已被禁用"
        )

    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    await db.commit()

    # 生成令牌
    token_data = create_user_token_data(user)
    tokens = jwt_handler.create_token_pair(token_data)

    return JSONResponse(content={
        "access_token": tokens["access_token"],
        "refresh_token": tokens["refresh_token"],
        "token_type": "bearer",
        "expires_in": jwt_handler.access_token_expire_minutes * 60,
        "user": {
            "id": str(user.id),
            "email": user.email,
            "display_name": user.display_name,
            "is_active": user.is_active
        }
    })


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(token_data: TokenRefresh, db: AsyncSession = Depends(get_db)):
    """刷新访问令牌"""
    
    # 验证刷新令牌
    payload = jwt_handler.verify_token(token_data.refresh_token)
    if not payload or payload.get("type") != "refresh":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的刷新令牌"
        )
    
    # 获取用户信息
    user_id = payload.get("sub")
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalar_one_or_none()
    
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户不存在或已被禁用"
        )
    
    # 生成新的令牌对
    new_token_data = create_user_token_data(user)
    tokens = jwt_handler.create_token_pair(new_token_data)
    
    return TokenResponse(
        access_token=tokens["access_token"],
        refresh_token=tokens["refresh_token"],
        expires_in=jwt_handler.access_token_expire_minutes * 60,
        user=user.to_dict()
    )


@router.post("/google", response_model=TokenResponse)
async def google_auth(auth_data: GoogleAuthRequest, db: AsyncSession = Depends(get_db)):
    """Google OAuth登录"""
    
    # TODO: 验证Google ID Token
    # 这里需要使用google-auth库验证token
    # 暂时返回错误，等待实现
    
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Google登录功能正在开发中"
    )


@router.post("/logout")
async def logout(token: str = Depends(security)):
    """用户登出"""
    
    # TODO: 将令牌加入黑名单
    # 这里需要Redis支持
    
    return {"message": "登出成功"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(lambda: None)  # 临时占位符，实际使用时需要正确的依赖
):
    """获取当前用户信息"""
    # TODO: 实现获取当前用户信息
    # 这个端点需要在main.py中使用正确的依赖注入
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )
