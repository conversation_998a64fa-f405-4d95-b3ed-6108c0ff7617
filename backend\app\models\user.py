from sqlalchemy import Column, String, DateTime, Boolean, Text, Integer
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime

from ..database.connection import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String(255), unique=True, nullable=False, index=True)
    display_name = Column(String(100))
    photo_url = Column(String(500))
    phone_number = Column(String(20))
    
    # 认证相关
    hashed_password = Column(String(255))  # 本地注册用户的密码哈希
    is_email_verified = Column(Boolean, default=False)
    email_verification_token = Column(String(255))
    password_reset_token = Column(String(255))
    password_reset_expires = Column(DateTime)
    
    # OAuth相关
    google_id = Column(String(100), unique=True, index=True)
    oauth_provider = Column(String(50))  # google, qq, 163等
    oauth_id = Column(String(100))
    
    # 用户偏好设置（SQLite使用TEXT存储JSON）
    preferences = Column(Text, default="{}")
    timezone = Column(String(50), default="Asia/Shanghai")
    language = Column(String(10), default="zh-CN")
    currency = Column(String(10), default="CNY")
    
    # 状态字段
    is_active = Column(Boolean, default=True)
    is_premium = Column(Boolean, default=False)
    premium_expires = Column(DateTime)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))
    
    # 同步相关
    sync_id = Column(String(100), unique=True)
    last_sync = Column(DateTime(timezone=True))
    
    # 关系（暂时注释掉，等其他模型创建后再启用）
    # accounts = relationship("Account", back_populates="user", cascade="all, delete-orphan")
    # transactions = relationship("Transaction", back_populates="user", cascade="all, delete-orphan")
    # categories = relationship("Category", back_populates="user", cascade="all, delete-orphan")
    # budgets = relationship("Budget", back_populates="user", cascade="all, delete-orphan")
    # sync_logs = relationship("SyncLog", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email})>"

    def to_dict(self):
        """转换为字典"""
        import json

        # 处理preferences字段（从JSON字符串转换为字典）
        try:
            preferences = json.loads(self.preferences) if self.preferences else {}
        except (json.JSONDecodeError, TypeError):
            preferences = {}

        return {
            "id": str(self.id),
            "email": self.email,
            "display_name": self.display_name,
            "photo_url": self.photo_url,
            "phone_number": self.phone_number,
            "is_email_verified": self.is_email_verified,
            "preferences": preferences,
            "timezone": self.timezone,
            "language": self.language,
            "currency": self.currency,
            "is_active": self.is_active,
            "is_premium": self.is_premium,
            "premium_expires": self.premium_expires.isoformat() if self.premium_expires else None,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
        }

    @classmethod
    def create_from_oauth(cls, email: str, display_name: str = None, 
                         photo_url: str = None, provider: str = None, 
                         oauth_id: str = None):
        """从OAuth信息创建用户"""
        return cls(
            email=email,
            display_name=display_name,
            photo_url=photo_url,
            oauth_provider=provider,
            oauth_id=oauth_id,
            is_email_verified=True,  # OAuth用户默认邮箱已验证
            sync_id=str(uuid.uuid4()),
        )

    def update_last_login(self):
        """更新最后登录时间"""
        self.last_login = datetime.utcnow()

    def update_preferences(self, preferences: dict):
        """更新用户偏好设置"""
        import json

        try:
            current_prefs = json.loads(self.preferences) if self.preferences else {}
        except (json.JSONDecodeError, TypeError):
            current_prefs = {}

        current_prefs.update(preferences)
        self.preferences = json.dumps(current_prefs)

    def is_oauth_user(self) -> bool:
        """检查是否为OAuth用户"""
        return self.oauth_provider is not None

    def can_login_with_password(self) -> bool:
        """检查是否可以使用密码登录"""
        return self.hashed_password is not None

    def get_display_name(self) -> str:
        """获取显示名称"""
        return self.display_name or self.email.split('@')[0]


class UserSession(Base):
    """用户会话模型"""
    __tablename__ = "user_sessions"

    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), nullable=False, index=True)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=False, index=True)
    
    # 设备信息
    device_id = Column(String(255))
    device_name = Column(String(100))
    device_type = Column(String(50))  # mobile, desktop, tablet
    user_agent = Column(Text)
    ip_address = Column(String(45))
    
    # 会话状态
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_used = Column(DateTime(timezone=True), server_default=func.now())

    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id})>"

    def is_expired(self) -> bool:
        """检查会话是否过期"""
        return datetime.utcnow() > self.expires_at

    def update_last_used(self):
        """更新最后使用时间"""
        self.last_used = datetime.utcnow()

    def revoke(self):
        """撤销会话"""
        self.is_active = False
