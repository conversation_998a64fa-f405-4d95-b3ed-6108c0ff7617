from sqlalchemy import Column, String, Boolean, DateTime, Text, ForeignKey, Numeric
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import json

from ..database.base import Base


class UserAIConfig(Base):
    """用户AI配置模型"""
    __tablename__ = "user_ai_configs"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    
    # AI配置
    provider = Column(String(50), nullable=False)  # openai, azure_openai, anthropic, etc.
    model_name = Column(String(100), nullable=False)
    api_key = Column(String(500), nullable=False)  # 加密存储
    api_base = Column(String(200))
    api_version = Column(String(50))
    
    # 模型参数
    max_tokens = Column(Numeric(10, 0), default=1000)
    temperature = Column(Numeric(3, 2), default=0.3)
    timeout = Column(Numeric(10, 0), default=30)
    
    # 额外配置（JSON格式）
    extra_config = Column(Text)  # 存储特定提供商的额外配置
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    
    # 使用统计
    total_requests = Column(Numeric(15, 0), default=0)
    successful_requests = Column(Numeric(15, 0), default=0)
    failed_requests = Column(Numeric(15, 0), default=0)
    last_used = Column(DateTime(timezone=True))
    
    # 配置名称和描述
    config_name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系（暂时注释掉）
    # user = relationship("User", back_populates="ai_configs")

    def __repr__(self):
        return f"<UserAIConfig(id={self.id}, provider={self.provider}, model={self.model_name})>"

    def to_dict(self, include_sensitive=False):
        """转换为字典"""
        # 处理extra_config字段
        try:
            extra_config = json.loads(self.extra_config) if self.extra_config else {}
        except (json.JSONDecodeError, TypeError):
            extra_config = {}
        
        result = {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "provider": self.provider,
            "model_name": self.model_name,
            "api_base": self.api_base,
            "api_version": self.api_version,
            "max_tokens": int(self.max_tokens) if self.max_tokens else 1000,
            "temperature": float(self.temperature) if self.temperature else 0.3,
            "timeout": int(self.timeout) if self.timeout else 30,
            "extra_config": extra_config,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "total_requests": int(self.total_requests) if self.total_requests else 0,
            "successful_requests": int(self.successful_requests) if self.successful_requests else 0,
            "failed_requests": int(self.failed_requests) if self.failed_requests else 0,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "config_name": self.config_name,
            "description": self.description,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }
        
        # 是否包含敏感信息
        if include_sensitive:
            result["api_key"] = self.api_key
        else:
            # 只显示API密钥的前几位和后几位
            if self.api_key:
                key_len = len(self.api_key)
                if key_len > 8:
                    result["api_key_masked"] = f"{self.api_key[:4]}...{self.api_key[-4:]}"
                else:
                    result["api_key_masked"] = "*" * key_len
            else:
                result["api_key_masked"] = ""
        
        return result

    def update_extra_config(self, config: dict):
        """更新额外配置"""
        self.extra_config = json.dumps(config)

    def get_extra_config(self) -> dict:
        """获取额外配置"""
        try:
            return json.loads(self.extra_config) if self.extra_config else {}
        except (json.JSONDecodeError, TypeError):
            return {}

    def set_as_default(self):
        """设置为默认配置"""
        self.is_default = True

    def deactivate(self):
        """停用配置"""
        self.is_active = False

    def activate(self):
        """激活配置"""
        self.is_active = True

    def increment_request_count(self, success: bool = True):
        """增加请求计数"""
        from decimal import Decimal
        
        if self.total_requests is None:
            self.total_requests = Decimal('0')
        if self.successful_requests is None:
            self.successful_requests = Decimal('0')
        if self.failed_requests is None:
            self.failed_requests = Decimal('0')
            
        self.total_requests += Decimal('1')
        if success:
            self.successful_requests += Decimal('1')
        else:
            self.failed_requests += Decimal('1')
        
        self.last_used = datetime.utcnow()

    def get_success_rate(self) -> float:
        """获取成功率"""
        if not self.total_requests or self.total_requests == 0:
            return 0.0
        return float(self.successful_requests) / float(self.total_requests) * 100

    def is_healthy(self) -> bool:
        """检查配置是否健康"""
        if not self.is_active:
            return False
        
        # 如果有使用记录，检查成功率
        if self.total_requests and self.total_requests > 0:
            success_rate = self.get_success_rate()
            return success_rate >= 50.0  # 成功率低于50%认为不健康
        
        return True

    @classmethod
    def create_default_config(cls, user_id: str, provider: str = "openai"):
        """创建默认配置"""
        from ..services.ai_config import DefaultAIConfigs
        
        default_configs = DefaultAIConfigs.get_default_configs()
        provider_config = default_configs.get(provider, {})
        
        return cls(
            user_id=user_id,
            provider=provider,
            model_name=provider_config.get("default_model", "gpt-3.5-turbo"),
            api_key="",  # 需要用户填写
            api_base=provider_config.get("api_base", ""),
            max_tokens=provider_config.get("max_tokens", 1000),
            temperature=provider_config.get("temperature", 0.3),
            config_name=f"默认{provider}配置",
            description=f"系统创建的默认{provider}配置",
            is_default=True,
        )


class AIUsageLog(Base):
    """AI使用日志模型"""
    __tablename__ = "ai_usage_logs"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    config_id = Column(String(36), ForeignKey("user_ai_configs.id"), nullable=False, index=True)
    
    # 请求信息
    request_type = Column(String(50), nullable=False)  # categorization, analysis, etc.
    input_tokens = Column(Numeric(10, 0))
    output_tokens = Column(Numeric(10, 0))
    total_tokens = Column(Numeric(10, 0))
    
    # 响应信息
    response_time = Column(Numeric(10, 3))  # 响应时间（秒）
    success = Column(Boolean, nullable=False)
    error_message = Column(Text)
    
    # 成本信息（如果有）
    estimated_cost = Column(Numeric(10, 6))  # 估算成本
    currency = Column(String(3), default="USD")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系（暂时注释掉）
    # user = relationship("User", back_populates="ai_usage_logs")
    # config = relationship("UserAIConfig", back_populates="usage_logs")

    def __repr__(self):
        return f"<AIUsageLog(id={self.id}, type={self.request_type}, success={self.success})>"

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "config_id": str(self.config_id),
            "request_type": self.request_type,
            "input_tokens": int(self.input_tokens) if self.input_tokens else 0,
            "output_tokens": int(self.output_tokens) if self.output_tokens else 0,
            "total_tokens": int(self.total_tokens) if self.total_tokens else 0,
            "response_time": float(self.response_time) if self.response_time else 0.0,
            "success": self.success,
            "error_message": self.error_message,
            "estimated_cost": float(self.estimated_cost) if self.estimated_cost else 0.0,
            "currency": self.currency,
            "created_at": self.created_at.isoformat(),
        }
