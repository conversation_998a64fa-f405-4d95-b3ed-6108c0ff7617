# AI记账应用 (AI Accounting App)

一个功能完整的AI驱动记账应用，支持智能分类、语音记账、拍照识别等功能。

## 功能特性

### 核心功能
- ✅ 手动记账录入
- ✅ 语音识别记账
- ✅ 拍照识别发票/收据
- ✅ AI智能分类账单
- ✅ 多账户管理
- ✅ 预算管理和提醒
- ✅ 财务报表和分析
- ✅ 离线数据存储
- ✅ 云端数据同步

### AI功能
- 🤖 智能交易分类
- 🤖 消费习惯分析
- 🤖 异常消费提醒
- 🤖 预算建议
- 🤖 财务规划建议

### 用户管理
- 🔐 多平台邮箱登录（Gmail、QQ、163等）
- 🔐 JWT安全认证
- 🔐 离线登录状态保持
- ☁️ 数据云端同步

## 技术架构

### 前端 (Flutter)
```
ai_accounting_app/
├── lib/
│   ├── main.dart
│   ├── models/          # 数据模型
│   ├── services/        # 业务逻辑服务
│   ├── providers/       # 状态管理
│   ├── screens/         # 页面UI
│   ├── widgets/         # 通用组件
│   ├── utils/           # 工具类
│   └── constants/       # 常量定义
├── assets/              # 资源文件
└── test/               # 测试文件
```

### 后端 (FastAPI)
```
backend/
├── app/
│   ├── main.py
│   ├── models/          # 数据库模型
│   ├── schemas/         # Pydantic模型
│   ├── api/             # API路由
│   ├── services/        # 业务逻辑
│   ├── auth/            # 认证相关
│   ├── ai/              # AI功能模块
│   └── database/        # 数据库配置
├── requirements.txt
└── Dockerfile
```

## 开发计划

1. **项目架构设计与初始化** ⏳
2. **用户认证系统开发**
3. **本地数据库设计**
4. **记账核心功能开发**
5. **AI智能分类系统**
6. **拍照识别功能**
7. **预算管理系统**
8. **财务报表与分析**
9. **数据同步系统**
10. **UI/UX界面开发**
11. **测试与优化**

## 快速开始

### 环境要求
- Flutter SDK >= 3.0
- Python >= 3.9
- PostgreSQL >= 13
- Redis >= 6.0

### 安装步骤
```bash
# 克隆项目
git clone <repository-url>

# 安装Flutter依赖
cd ai_accounting_app
flutter pub get

# 安装Python依赖
cd ../backend
pip install -r requirements.txt

# 启动后端服务
uvicorn app.main:app --reload

# 运行Flutter应用
cd ../ai_accounting_app
flutter run
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License
