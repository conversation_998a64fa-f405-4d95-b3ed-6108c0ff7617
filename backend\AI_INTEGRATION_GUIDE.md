# AI智能分类系统集成指南

## 概述

本系统已成功集成了AI智能分类功能，支持多种AI提供商，为用户提供智能的交易分类和消费分析服务。

## 🚀 主要功能

### 1. 多AI提供商支持
- **OpenAI**: GPT-3.5/GPT-4系列模型
- **Azure OpenAI**: 微软Azure平台的OpenAI服务
- **Anthropic Claude**: Claude系列模型
- **Google Gemini**: Google的Gemini模型
- **百度千帆**: 百度的AI大模型平台
- **阿里云灵积**: 阿里巴巴的AI模型服务
- **智谱AI**: 清华智谱的GLM系列模型
- **月之暗面**: Moonshot AI的模型
- **DeepSeek**: DeepSeek的AI模型
- **自定义提供商**: 支持用户自定义AI接口

### 2. AI配置管理
- 用户可以配置多个AI服务
- 支持设置默认AI配置
- 提供配置测试功能
- 记录使用统计和成功率

### 3. 智能交易分类
- 基于交易描述自动分类
- 考虑交易金额和类型
- 参考用户历史分类习惯
- 提供分类置信度评分
- 支持批量分类处理

### 4. 消费模式分析
- 分析用户消费习惯
- 检测异常消费行为
- 提供财务健康评估
- 生成个性化建议

## 📋 API端点

### AI配置管理

#### 获取支持的AI提供商
```http
GET /api/v1/ai-config/providers
Authorization: Bearer <token>
```

#### 创建AI配置
```http
POST /api/v1/ai-config/configs
Authorization: Bearer <token>
Content-Type: application/json

{
  "provider": "openai",
  "model_name": "gpt-3.5-turbo",
  "api_key": "your-api-key",
  "api_base": "https://api.openai.com/v1",
  "max_tokens": 1000,
  "temperature": 0.3,
  "timeout": 30,
  "config_name": "我的OpenAI配置",
  "description": "用于智能分类的OpenAI配置",
  "is_default": true
}
```

#### 获取用户AI配置列表
```http
GET /api/v1/ai-config/configs
Authorization: Bearer <token>
```

#### 测试AI配置
```http
POST /api/v1/ai-config/configs/{config_id}/test
Authorization: Bearer <token>
Content-Type: application/json

{
  "test_message": "你好，这是一个测试消息。"
}
```

### AI智能分类

#### 单个交易分类
```http
POST /api/v1/ai-categorization/categorize
Authorization: Bearer <token>
Content-Type: application/json

{
  "description": "麦当劳午餐",
  "amount": 35.5,
  "transaction_type": "expense",
  "config_id": "optional-config-id"
}
```

#### 批量交易分类
```http
POST /api/v1/ai-categorization/categorize/batch
Authorization: Bearer <token>
Content-Type: application/json

{
  "transactions": [
    {
      "description": "星巴克咖啡",
      "amount": 32.0,
      "transaction_type": "expense"
    },
    {
      "description": "地铁卡充值",
      "amount": 100.0,
      "transaction_type": "expense"
    }
  ],
  "config_id": "optional-config-id"
}
```

#### 消费模式分析
```http
POST /api/v1/ai-categorization/analyze/spending
Authorization: Bearer <token>
Content-Type: application/json

{
  "start_date": "2024-01-01T00:00:00Z",
  "end_date": "2024-01-31T23:59:59Z",
  "time_period": "month",
  "config_id": "optional-config-id"
}
```

## 🔧 配置说明

### AI提供商配置参数

#### OpenAI
```json
{
  "provider": "openai",
  "model_name": "gpt-3.5-turbo",
  "api_key": "sk-...",
  "api_base": "https://api.openai.com/v1",
  "max_tokens": 1000,
  "temperature": 0.3
}
```

#### Azure OpenAI
```json
{
  "provider": "azure_openai",
  "model_name": "gpt-35-turbo",
  "api_key": "your-azure-key",
  "api_base": "https://your-resource.openai.azure.com",
  "api_version": "2023-12-01-preview",
  "extra_config": {
    "azure_deployment": "your-deployment-name"
  }
}
```

#### Anthropic Claude
```json
{
  "provider": "anthropic",
  "model_name": "claude-3-sonnet-20240229",
  "api_key": "sk-ant-...",
  "api_base": "https://api.anthropic.com",
  "max_tokens": 1000,
  "temperature": 0.3
}
```

#### 自定义提供商
```json
{
  "provider": "custom",
  "model_name": "custom-model",
  "api_key": "your-api-key",
  "api_base": "https://your-api-endpoint.com",
  "extra_config": {
    "custom_headers": {
      "X-Custom-Header": "value"
    },
    "request_format": "openai_compatible"
  }
}
```

## 🧪 测试

运行集成测试：
```bash
cd backend
python test_ai_integration.py
```

测试将验证：
- AI提供商列表获取
- AI配置创建和管理
- 智能分类功能（需要真实API密钥）
- 批量分类处理

## 📊 使用统计

系统会自动记录：
- 每个配置的使用次数
- 成功/失败请求统计
- 响应时间统计
- 估算成本（如果支持）

## 🔒 安全考虑

1. **API密钥安全**: 
   - API密钥在数据库中应加密存储（当前为明文，生产环境需要加密）
   - 前端显示时进行脱敏处理

2. **请求限制**:
   - 批量分类限制最多50个交易
   - 实施请求频率限制

3. **错误处理**:
   - 完善的异常处理机制
   - 详细的错误日志记录

## 🚀 部署建议

1. **环境变量**:
   ```bash
   # 可选：设置默认AI配置
   DEFAULT_AI_PROVIDER=openai
   DEFAULT_AI_MODEL=gpt-3.5-turbo
   ```

2. **依赖安装**:
   ```bash
   pip install -r requirements.txt
   ```

3. **数据库迁移**:
   系统会自动创建AI相关的数据表：
   - `user_ai_configs`: 用户AI配置
   - `ai_usage_logs`: AI使用日志

## 📈 未来扩展

1. **更多AI提供商**: 可以轻松添加新的AI提供商支持
2. **模型微调**: 支持基于用户数据的模型微调
3. **智能预算**: 基于AI分析的智能预算建议
4. **异常检测**: 更高级的异常消费检测算法
5. **多语言支持**: 支持多种语言的交易描述分类

## 🐛 故障排除

### 常见问题

1. **AI分类失败**:
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 查看错误日志获取详细信息

2. **配置测试失败**:
   - 验证API端点URL
   - 检查模型名称是否正确
   - 确认API密钥权限

3. **分类结果不准确**:
   - 增加用户历史数据
   - 调整temperature参数
   - 考虑使用更高级的模型

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看AI相关日志
grep "AI" logs/app.log
```

## 📞 技术支持

如有问题，请查看：
1. API文档: http://localhost:8000/docs
2. 错误日志: 检查应用日志文件
3. 测试脚本: 运行test_ai_integration.py进行诊断
