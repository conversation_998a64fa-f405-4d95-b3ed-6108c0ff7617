// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'AI记账助手';
  static const String appVersion = '1.0.0';
  
  // API配置
  static const String baseUrl = 'https://api.aiaccounting.com';
  static const String apiVersion = 'v1';
  
  // 本地存储键
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_info';
  static const String settingsKey = 'app_settings';
  static const String lastSyncKey = 'last_sync_time';
  
  // 数据库配置
  static const String databaseName = 'ai_accounting.db';
  static const int databaseVersion = 1;
  
  // 分页配置
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // 文件上传配置
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> supportedImageTypes = ['jpg', 'jpeg', 'png'];
  
  // 语音识别配置
  static const int maxRecordingDuration = 60; // 秒
  static const String defaultLanguage = 'zh-CN';
  
  // 同步配置
  static const int syncInterval = 300; // 5分钟
  static const int maxRetryCount = 3;
}

// 交易类型
enum TransactionType {
  income,   // 收入
  expense,  // 支出
  transfer, // 转账
}

// 账户类型
enum AccountType {
  cash,        // 现金
  bankCard,    // 银行卡
  creditCard,  // 信用卡
  alipay,      // 支付宝
  wechat,      // 微信
  other,       // 其他
}

// 预算周期
enum BudgetPeriod {
  daily,    // 日
  weekly,   // 周
  monthly,  // 月
  yearly,   // 年
}

// 同步状态
enum SyncStatus {
  pending,    // 待同步
  syncing,    // 同步中
  synced,     // 已同步
  failed,     // 同步失败
}

// 应用主题色彩
class AppColors {
  static const int primaryColor = 0xFF2196F3;
  static const int accentColor = 0xFF03DAC6;
  static const int errorColor = 0xFFB00020;
  static const int successColor = 0xFF4CAF50;
  static const int warningColor = 0xFFFF9800;
  
  // 收入支出颜色
  static const int incomeColor = 0xFF4CAF50;
  static const int expenseColor = 0xFFF44336;
  static const int transferColor = 0xFF2196F3;
}

// 默认分类
class DefaultCategories {
  static const List<Map<String, dynamic>> incomeCategories = [
    {'name': '工资', 'icon': 'work', 'color': 0xFF4CAF50},
    {'name': '奖金', 'icon': 'card_giftcard', 'color': 0xFF8BC34A},
    {'name': '投资收益', 'icon': 'trending_up', 'color': 0xFF009688},
    {'name': '兼职', 'icon': 'business_center', 'color': 0xFF607D8B},
    {'name': '其他收入', 'icon': 'attach_money', 'color': 0xFF795548},
  ];
  
  static const List<Map<String, dynamic>> expenseCategories = [
    {'name': '餐饮', 'icon': 'restaurant', 'color': 0xFFFF5722},
    {'name': '交通', 'icon': 'directions_car', 'color': 0xFF3F51B5},
    {'name': '购物', 'icon': 'shopping_cart', 'color': 0xFFE91E63},
    {'name': '娱乐', 'icon': 'movie', 'color': 0xFF9C27B0},
    {'name': '医疗', 'icon': 'local_hospital', 'color': 0xFFF44336},
    {'name': '教育', 'icon': 'school', 'color': 0xFF2196F3},
    {'name': '住房', 'icon': 'home', 'color': 0xFF607D8B},
    {'name': '通讯', 'icon': 'phone', 'color': 0xFF795548},
    {'name': '其他支出', 'icon': 'more_horiz', 'color': 0xFF9E9E9E},
  ];
}
