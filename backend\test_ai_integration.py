#!/usr/bin/env python3
"""
AI功能集成测试脚本
测试AI配置管理和智能分类功能
"""

import asyncio
import httpx
import json
from typing import Dict, Any

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword123"

class AIIntegrationTester:
    def __init__(self):
        self.client = httpx.AsyncClient(base_url=BASE_URL)
        self.access_token = None
        self.user_id = None
        self.ai_config_id = None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    async def register_and_login(self):
        """注册并登录测试用户"""
        print("🔐 注册并登录测试用户...")
        
        # 尝试注册
        register_data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD,
            "full_name": "AI测试用户"
        }
        
        try:
            response = await self.client.post("/api/v1/auth/register", json=register_data)
            if response.status_code == 201:
                print("✅ 用户注册成功")
            elif response.status_code == 400:
                print("ℹ️ 用户已存在，跳过注册")
            else:
                print(f"❌ 注册失败: {response.text}")
        except Exception as e:
            print(f"❌ 注册请求失败: {e}")

        # 登录
        login_data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD
        }

        try:
            response = await self.client.post("/api/v1/auth/login", json=login_data)
            if response.status_code == 200:
                result = response.json()
                self.access_token = result["access_token"]
                self.user_id = result["user"]["id"]
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 登录请求失败: {e}")
            return False

    def get_auth_headers(self):
        """获取认证头"""
        return {"Authorization": f"Bearer {self.access_token}"}

    async def test_ai_providers(self):
        """测试获取AI提供商列表"""
        print("\n🤖 测试AI提供商列表...")
        
        try:
            response = await self.client.get(
                "/api/v1/ai-config/providers",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                providers = result.get("providers", [])
                print(f"✅ 获取到 {len(providers)} 个AI提供商:")
                for provider in providers[:3]:  # 只显示前3个
                    print(f"   - {provider['name']}: {provider['description']}")
                return True
            else:
                print(f"❌ 获取AI提供商失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

    async def test_create_ai_config(self):
        """测试创建AI配置"""
        print("\n⚙️ 测试创建AI配置...")
        
        # 创建一个测试配置（使用OpenAI）
        config_data = {
            "provider": "openai",
            "model_name": "gpt-3.5-turbo",
            "api_key": "sk-test-key-for-demo-purposes-only",
            "api_base": "https://api.openai.com/v1",
            "max_tokens": 1000,
            "temperature": 0.3,
            "timeout": 30,
            "extra_config": {},
            "config_name": "测试OpenAI配置",
            "description": "用于测试的OpenAI配置",
            "is_default": True
        }
        
        try:
            response = await self.client.post(
                "/api/v1/ai-config/configs",
                json=config_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                self.ai_config_id = result["config"]["id"]
                print("✅ AI配置创建成功")
                print(f"   配置ID: {self.ai_config_id}")
                print(f"   配置名称: {result['config']['config_name']}")
                return True
            else:
                print(f"❌ 创建AI配置失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

    async def test_get_ai_configs(self):
        """测试获取AI配置列表"""
        print("\n📋 测试获取AI配置列表...")
        
        try:
            response = await self.client.get(
                "/api/v1/ai-config/configs",
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                configs = result.get("configs", [])
                print(f"✅ 获取到 {len(configs)} 个AI配置:")
                for config in configs:
                    print(f"   - {config['config_name']} ({config['provider']})")
                    print(f"     模型: {config['model_name']}")
                    print(f"     状态: {'活跃' if config['is_active'] else '非活跃'}")
                    print(f"     默认: {'是' if config['is_default'] else '否'}")
                return True
            else:
                print(f"❌ 获取AI配置失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            return False

    async def create_test_categories(self):
        """创建测试分类"""
        print("\n📂 创建测试分类...")
        
        categories = [
            {"name": "餐饮", "category_type": "expense", "description": "餐厅、外卖等饮食消费"},
            {"name": "交通", "category_type": "expense", "description": "公交、地铁、打车等交通费用"},
            {"name": "购物", "category_type": "expense", "description": "日用品、服装等购物消费"},
            {"name": "娱乐", "category_type": "expense", "description": "电影、游戏等娱乐消费"},
            {"name": "工资", "category_type": "income", "description": "工资收入"},
            {"name": "其他", "category_type": "expense", "description": "其他未分类支出"}
        ]
        
        created_count = 0
        for category_data in categories:
            try:
                response = await self.client.post(
                    "/api/v1/categories",
                    json=category_data,
                    headers=self.get_auth_headers()
                )
                
                if response.status_code == 201:
                    created_count += 1
                elif response.status_code == 400 and "已存在" in response.text:
                    # 分类已存在，跳过
                    pass
                else:
                    print(f"❌ 创建分类 {category_data['name']} 失败: {response.text}")
            except Exception as e:
                print(f"❌ 创建分类请求失败: {e}")
        
        print(f"✅ 创建了 {created_count} 个新分类")
        return True

    async def test_transaction_categorization(self):
        """测试交易智能分类"""
        print("\n🧠 测试交易智能分类...")
        
        # 测试交易数据
        test_transactions = [
            {
                "description": "麦当劳午餐",
                "amount": 35.5,
                "transaction_type": "expense"
            },
            {
                "description": "滴滴打车到公司",
                "amount": 18.0,
                "transaction_type": "expense"
            },
            {
                "description": "淘宝购买手机壳",
                "amount": 29.9,
                "transaction_type": "expense"
            }
        ]
        
        successful_count = 0
        for i, transaction in enumerate(test_transactions):
            print(f"\n   测试交易 {i+1}: {transaction['description']}")
            
            try:
                response = await self.client.post(
                    "/api/v1/ai-categorization/categorize",
                    json=transaction,
                    headers=self.get_auth_headers()
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        categorization = result["result"]
                        print(f"   ✅ 分类结果: {categorization['category_name']}")
                        print(f"   置信度: {categorization['confidence']:.2f}")
                        print(f"   理由: {categorization['reasoning']}")
                        successful_count += 1
                    else:
                        print(f"   ❌ 分类失败: {result}")
                else:
                    print(f"   ❌ 请求失败: {response.text}")
            except Exception as e:
                print(f"   ❌ 请求异常: {e}")
        
        print(f"\n✅ 成功分类 {successful_count}/{len(test_transactions)} 个交易")
        return successful_count > 0

    async def test_batch_categorization(self):
        """测试批量分类"""
        print("\n📦 测试批量交易分类...")
        
        batch_data = {
            "transactions": [
                {
                    "description": "星巴克咖啡",
                    "amount": 32.0,
                    "transaction_type": "expense"
                },
                {
                    "description": "地铁卡充值",
                    "amount": 100.0,
                    "transaction_type": "expense"
                },
                {
                    "description": "电影票",
                    "amount": 45.0,
                    "transaction_type": "expense"
                }
            ]
        }
        
        try:
            response = await self.client.post(
                "/api/v1/ai-categorization/categorize/batch",
                json=batch_data,
                headers=self.get_auth_headers()
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    summary = result["summary"]
                    print(f"✅ 批量分类完成:")
                    print(f"   总数: {summary['total']}")
                    print(f"   成功: {summary['successful']}")
                    print(f"   失败: {summary['failed']}")
                    
                    # 显示部分结果
                    for i, res in enumerate(result["results"][:2]):
                        if res["success"]:
                            cat = res["result"]
                            print(f"   交易{i+1}: {cat['category_name']} (置信度: {cat['confidence']:.2f})")
                    
                    return True
                else:
                    print(f"❌ 批量分类失败: {result}")
                    return False
            else:
                print(f"❌ 请求失败: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False

    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始AI功能集成测试\n")
        
        # 登录
        if not await self.register_and_login():
            print("❌ 登录失败，终止测试")
            return False
        
        # 测试AI提供商列表
        if not await self.test_ai_providers():
            print("❌ AI提供商测试失败")
            return False
        
        # 创建AI配置
        if not await self.test_create_ai_config():
            print("❌ AI配置创建测试失败")
            return False
        
        # 获取AI配置列表
        if not await self.test_get_ai_configs():
            print("❌ AI配置列表测试失败")
            return False
        
        # 创建测试分类
        await self.create_test_categories()
        
        # 测试单个交易分类（注意：这需要真实的API密钥才能工作）
        print("\n⚠️ 注意: 以下测试需要真实的AI API密钥才能正常工作")
        print("   当前使用的是测试密钥，预期会失败")
        
        await self.test_transaction_categorization()
        await self.test_batch_categorization()
        
        print("\n🎉 AI功能集成测试完成!")
        print("\n📝 测试总结:")
        print("✅ AI提供商列表获取正常")
        print("✅ AI配置管理功能正常")
        print("✅ API端点响应正常")
        print("⚠️ AI分类功能需要真实API密钥才能完全测试")
        
        return True


async def main():
    """主函数"""
    async with AIIntegrationTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
