from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from ..auth.dependencies import get_current_user
from ..database.connection import get_db
from ..models.user import User
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


# 请求模型
class TransactionCreate(BaseModel):
    account_id: str
    to_account_id: Optional[str] = None  # 转账目标账户
    category_id: str
    type: str  # income, expense, transfer
    amount: float
    currency: str = "CNY"
    description: Optional[str] = None
    note: Optional[str] = None
    transaction_date: datetime
    location: Optional[str] = None
    tags: Optional[List[str]] = None


class TransactionUpdate(BaseModel):
    account_id: Optional[str] = None
    to_account_id: Optional[str] = None
    category_id: Optional[str] = None
    amount: Optional[float] = None
    description: Optional[str] = None
    note: Optional[str] = None
    transaction_date: Optional[datetime] = None
    location: Optional[str] = None
    tags: Optional[List[str]] = None


# 响应模型
class TransactionResponse(BaseModel):
    id: str
    account_id: str
    to_account_id: Optional[str]
    category_id: str
    type: str
    amount: float
    currency: str
    description: Optional[str]
    note: Optional[str]
    transaction_date: datetime
    location: Optional[str]
    tags: Optional[List[str]]
    receipt_image_url: Optional[str]
    sync_status: str
    created_at: datetime
    updated_at: datetime


@router.get("/", response_model=List[TransactionResponse])
async def get_transactions(
    skip: int = 0,
    limit: int = 20,
    account_id: Optional[str] = None,
    category_id: Optional[str] = None,
    type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取交易记录列表"""
    # TODO: 实现获取交易记录列表
    return []


@router.post("/", response_model=TransactionResponse)
async def create_transaction(
    transaction_data: TransactionCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新交易记录"""
    # TODO: 实现创建交易记录
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定交易记录"""
    # TODO: 实现获取单个交易记录
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.put("/{transaction_id}", response_model=TransactionResponse)
async def update_transaction(
    transaction_id: str,
    transaction_data: TransactionUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新交易记录"""
    # TODO: 实现更新交易记录
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.delete("/{transaction_id}")
async def delete_transaction(
    transaction_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除交易记录"""
    # TODO: 实现删除交易记录
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )
