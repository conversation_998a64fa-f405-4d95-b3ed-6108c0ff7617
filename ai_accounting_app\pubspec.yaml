name: ai_accounting_app
description: "AI驱动的智能记账应用，支持语音记账、拍照识别、智能分类等功能"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI组件
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # 状态管理
  provider: ^6.1.2
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # 网络请求
  dio: ^5.4.0
  retrofit: ^4.1.0
  json_annotation: ^4.8.1

  # 本地存储
  sqflite: ^2.3.0
  shared_preferences: ^2.2.2
  path: ^1.8.3

  # 认证相关
  google_sign_in: ^6.2.1
  # firebase_auth: ^4.15.3
  # firebase_core: ^2.24.2

  # AI功能
  speech_to_text: ^6.6.0
  image_picker: ^1.0.4
  camera: ^0.10.5+5
  google_ml_kit: ^0.16.3

  # 图表和UI
  fl_chart: ^0.66.0
  intl: ^0.19.0
  flutter_localizations:
    sdk: flutter

  # 工具类
  uuid: ^4.2.1
  crypto: ^3.0.3
  http: ^1.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

  # 代码生成
  build_runner: ^2.4.7
  retrofit_generator: ^8.1.0
  json_serializable: ^6.7.1

  # 测试相关
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # 资源文件
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
