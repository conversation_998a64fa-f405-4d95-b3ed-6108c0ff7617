import json
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from loguru import logger

from .ai_client import AIClientFactory, BaseAIClient
from .ai_config import AIModelConfig, AIProvider
from ..models.category import Category
from ..models.transaction import Transaction


class TransactionCategorizationService:
    """交易分类服务"""
    
    def __init__(self, ai_config: AIModelConfig):
        self.ai_config = ai_config
        self.ai_client: Optional[BaseAIClient] = None
    
    async def initialize(self):
        """初始化AI客户端"""
        try:
            self.ai_client = AIClientFactory.create_client(self.ai_config)
            logger.info(f"AI分类服务初始化成功，使用提供商: {self.ai_config.provider}")
        except Exception as e:
            logger.error(f"AI分类服务初始化失败: {e}")
            raise
    
    async def close(self):
        """关闭AI客户端"""
        if self.ai_client:
            await self.ai_client.close()
    
    def _build_categorization_prompt(self, 
                                   transaction_description: str,
                                   amount: float,
                                   transaction_type: str,
                                   available_categories: List[Dict[str, Any]],
                                   user_history: List[Dict[str, Any]] = None) -> str:
        """构建分类提示词"""
        
        # 构建可用分类列表
        categories_text = ""
        for cat in available_categories:
            categories_text += f"- {cat['name']} ({cat['category_type']})\n"
            if cat.get('description'):
                categories_text += f"  描述: {cat['description']}\n"
        
        # 构建历史记录参考
        history_text = ""
        if user_history:
            history_text = "\n\n## 用户历史记录参考\n"
            for record in user_history[:5]:  # 只取最近5条
                history_text += f"- 描述: {record['description']}, 分类: {record['category_name']}, 金额: {record['amount']}\n"
        
        prompt = f"""
你是一个专业的财务分类助手。请根据交易信息为用户的消费记录选择最合适的分类。

## 交易信息
- 描述: {transaction_description}
- 金额: {amount}
- 类型: {transaction_type}

## 可用分类
{categories_text}
{history_text}

## 分析要求
1. 仔细分析交易描述中的关键词
2. 考虑交易金额和类型
3. 参考用户的历史分类习惯
4. 选择最匹配的分类

## 输出格式
请严格按照以下JSON格式输出，不要包含任何其他文字：

{{
    "category_name": "选择的分类名称",
    "confidence": 0.95,
    "reasoning": "选择此分类的原因",
    "keywords": ["关键词1", "关键词2"],
    "alternative_categories": [
        {{
            "name": "备选分类1",
            "confidence": 0.8,
            "reason": "备选原因"
        }}
    ]
}}

confidence值应该在0.0到1.0之间，表示分类的置信度。
"""
        return prompt
    
    async def categorize_transaction(self,
                                   transaction_description: str,
                                   amount: float,
                                   transaction_type: str,
                                   available_categories: List[Dict[str, Any]],
                                   user_history: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """对交易进行分类"""
        
        if not self.ai_client:
            raise RuntimeError("AI客户端未初始化")
        
        try:
            # 构建提示词
            system_prompt = "你是一个专业的财务分类助手，擅长根据交易描述进行准确分类。"
            user_prompt = self._build_categorization_prompt(
                transaction_description, amount, transaction_type, 
                available_categories, user_history
            )
            
            # 格式化消息
            messages = self.ai_client.format_messages(user_prompt, system_prompt)
            
            # 调用AI接口
            response = await self.ai_client.chat_completion(messages)
            
            # 解析响应
            ai_response = response["choices"][0]["message"]["content"]
            result = self._parse_categorization_response(ai_response)
            
            # 验证分类是否存在
            category_names = [cat["name"] for cat in available_categories]
            if result["category_name"] not in category_names:
                # 如果AI选择的分类不存在，选择最相似的
                result["category_name"] = self._find_most_similar_category(
                    result["category_name"], category_names
                )
                result["confidence"] = max(0.1, result["confidence"] - 0.3)
            
            logger.info(f"交易分类完成: {transaction_description} -> {result['category_name']} (置信度: {result['confidence']})")
            return result
            
        except Exception as e:
            logger.error(f"交易分类失败: {e}")
            # 返回默认分类结果
            return {
                "category_name": "其他",
                "confidence": 0.1,
                "reasoning": f"AI分类失败，使用默认分类: {str(e)}",
                "keywords": [],
                "alternative_categories": []
            }
    
    def _parse_categorization_response(self, response: str) -> Dict[str, Any]:
        """解析AI分类响应"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                
                # 验证必需字段
                required_fields = ["category_name", "confidence", "reasoning"]
                for field in required_fields:
                    if field not in result:
                        raise ValueError(f"缺少必需字段: {field}")
                
                # 确保confidence在有效范围内
                result["confidence"] = max(0.0, min(1.0, float(result["confidence"])))
                
                # 确保可选字段存在
                result.setdefault("keywords", [])
                result.setdefault("alternative_categories", [])
                
                return result
            else:
                raise ValueError("响应中未找到有效的JSON格式")
                
        except Exception as e:
            logger.error(f"解析AI响应失败: {e}, 原始响应: {response}")
            # 尝试简单解析
            return {
                "category_name": "其他",
                "confidence": 0.1,
                "reasoning": "响应解析失败",
                "keywords": [],
                "alternative_categories": []
            }
    
    def _find_most_similar_category(self, target: str, available: List[str]) -> str:
        """找到最相似的分类"""
        if not available:
            return "其他"
        
        # 简单的字符串相似度匹配
        target_lower = target.lower()
        best_match = available[0]
        best_score = 0
        
        for category in available:
            category_lower = category.lower()
            # 计算简单的相似度分数
            if target_lower in category_lower or category_lower in target_lower:
                score = min(len(target_lower), len(category_lower)) / max(len(target_lower), len(category_lower))
                if score > best_score:
                    best_score = score
                    best_match = category
        
        return best_match
    
    async def analyze_spending_pattern(self, 
                                     transactions: List[Dict[str, Any]],
                                     time_period: str = "month") -> Dict[str, Any]:
        """分析消费模式"""
        
        if not self.ai_client:
            raise RuntimeError("AI客户端未初始化")
        
        try:
            # 构建消费分析提示词
            transactions_summary = self._build_transactions_summary(transactions)
            
            system_prompt = "你是一个专业的财务分析师，擅长分析用户的消费模式和习惯。"
            user_prompt = f"""
请分析以下用户的消费记录，提供专业的财务分析和建议。

## 消费记录摘要 ({time_period})
{transactions_summary}

## 分析要求
1. 消费模式分析（主要消费类别、频率等）
2. 异常消费检测（金额异常、频率异常等）
3. 消费趋势分析
4. 财务健康评估
5. 个性化建议

## 输出格式
请严格按照以下JSON格式输出：

{{
    "spending_pattern": {{
        "main_categories": [
            {{"category": "分类名", "amount": 1000, "percentage": 30.5}}
        ],
        "frequency_analysis": "消费频率分析",
        "peak_spending_times": ["时间段1", "时间段2"]
    }},
    "anomaly_detection": {{
        "unusual_transactions": [
            {{"description": "异常交易描述", "amount": 500, "reason": "异常原因"}}
        ],
        "spending_spikes": ["异常消费高峰时间"]
    }},
    "trends": {{
        "trend_direction": "increasing/decreasing/stable",
        "trend_description": "趋势描述"
    }},
    "financial_health": {{
        "score": 85,
        "assessment": "财务健康评估",
        "risk_factors": ["风险因素1", "风险因素2"]
    }},
    "recommendations": [
        {{"type": "budget", "message": "预算建议"}},
        {{"type": "saving", "message": "储蓄建议"}}
    ]
}}
"""
            
            messages = self.ai_client.format_messages(user_prompt, system_prompt)
            response = await self.ai_client.chat_completion(messages)
            
            ai_response = response["choices"][0]["message"]["content"]
            result = self._parse_analysis_response(ai_response)
            
            logger.info(f"消费模式分析完成，财务健康评分: {result.get('financial_health', {}).get('score', 'N/A')}")
            return result
            
        except Exception as e:
            logger.error(f"消费模式分析失败: {e}")
            return {
                "error": str(e),
                "spending_pattern": {},
                "anomaly_detection": {},
                "trends": {},
                "financial_health": {"score": 0, "assessment": "分析失败"},
                "recommendations": []
            }
    
    def _build_transactions_summary(self, transactions: List[Dict[str, Any]]) -> str:
        """构建交易摘要"""
        if not transactions:
            return "无交易记录"
        
        summary = f"总交易数: {len(transactions)}\n\n"
        
        # 按分类汇总
        category_summary = {}
        total_amount = 0
        
        for trans in transactions:
            category = trans.get("category_name", "未分类")
            amount = float(trans.get("amount", 0))
            
            if category not in category_summary:
                category_summary[category] = {"count": 0, "amount": 0}
            
            category_summary[category]["count"] += 1
            category_summary[category]["amount"] += amount
            total_amount += amount
        
        summary += f"总金额: {total_amount:.2f}\n\n"
        summary += "分类汇总:\n"
        
        for category, data in sorted(category_summary.items(), key=lambda x: x[1]["amount"], reverse=True):
            percentage = (data["amount"] / total_amount * 100) if total_amount > 0 else 0
            summary += f"- {category}: {data['amount']:.2f} ({data['count']}笔, {percentage:.1f}%)\n"
        
        return summary
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """解析分析响应"""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                raise ValueError("响应中未找到有效的JSON格式")
        except Exception as e:
            logger.error(f"解析分析响应失败: {e}")
            return {
                "error": str(e),
                "spending_pattern": {},
                "anomaly_detection": {},
                "trends": {},
                "financial_health": {"score": 0, "assessment": "解析失败"},
                "recommendations": []
            }
