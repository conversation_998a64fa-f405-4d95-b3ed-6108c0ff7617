version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: ai_accounting_postgres
    environment:
      POSTGRES_DB: ai_accounting
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai_accounting_network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ai_accounting_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai_accounting_network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # FastAPI应用
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai_accounting_api
    environment:
      - DATABASE_URL=********************************************/ai_accounting
      - REDIS_URL=redis://redis:6379/0
      - DEBUG=false
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - ai_accounting_network
    restart: unless-stopped

  # Celery Worker（异步任务处理）
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai_accounting_celery
    command: celery -A app.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=********************************************/ai_accounting
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - ai_accounting_network
    restart: unless-stopped

  # Celery Flower（任务监控）
  celery_flower:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai_accounting_flower
    command: celery -A app.celery_app flower --port=5555
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    ports:
      - "5555:5555"
    depends_on:
      - redis
      - celery_worker
    networks:
      - ai_accounting_network
    restart: unless-stopped

  # Nginx反向代理（生产环境）
  nginx:
    image: nginx:alpine
    container_name: ai_accounting_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - ai_accounting_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  ai_accounting_network:
    driver: bridge
