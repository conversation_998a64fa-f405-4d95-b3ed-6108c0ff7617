from sqlalchemy import Column, String, Numeric, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import json

from ..database.base import Base


class Transaction(Base):
    """交易记录模型"""
    __tablename__ = "transactions"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    account_id = Column(String(36), ForeignKey("accounts.id"), nullable=False, index=True)
    category_id = Column(String(36), ForeignKey("categories.id"), nullable=True, index=True)
    
    # 交易详情
    amount = Column(Numeric(15, 2), nullable=False)
    transaction_type = Column(String(20), nullable=False)  # income, expense, transfer
    description = Column(Text)
    notes = Column(Text)
    
    # 转账相关（如果是转账交易）
    to_account_id = Column(String(36), ForeignKey("accounts.id"), nullable=True)
    
    # 时间信息
    transaction_date = Column(DateTime(timezone=True), nullable=False)
    
    # 附件和标签
    receipt_url = Column(String(500))  # 收据图片URL
    tags = Column(Text)  # JSON格式的标签列表
    
    # 位置信息
    location = Column(String(200))
    latitude = Column(Numeric(10, 8))
    longitude = Column(Numeric(11, 8))

    # AI相关
    ai_category_confidence = Column(Numeric(3, 2))  # AI分类置信度
    ai_extracted_data = Column(Text)  # AI提取的数据（JSON格式）
    
    # 同步相关
    sync_id = Column(String(100), unique=True)
    last_sync = Column(DateTime(timezone=True))
    sync_status = Column(String(20), default="pending")  # pending, synced, conflict
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系（暂时注释掉）
    # user = relationship("User", back_populates="transactions")
    # account = relationship("Account", back_populates="transactions")
    # category = relationship("Category", back_populates="transactions")
    # to_account = relationship("Account", foreign_keys=[to_account_id])

    def __repr__(self):
        return f"<Transaction(id={self.id}, amount={self.amount}, type={self.transaction_type})>"

    def to_dict(self):
        """转换为字典"""
        # 处理tags字段（从JSON字符串转换为列表）
        try:
            tags = json.loads(self.tags) if self.tags else []
        except (json.JSONDecodeError, TypeError):
            tags = []
            
        # 处理AI提取的数据
        try:
            ai_data = json.loads(self.ai_extracted_data) if self.ai_extracted_data else {}
        except (json.JSONDecodeError, TypeError):
            ai_data = {}
            
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "account_id": str(self.account_id),
            "category_id": str(self.category_id) if self.category_id else None,
            "amount": float(self.amount),
            "transaction_type": self.transaction_type,
            "description": self.description,
            "notes": self.notes,
            "to_account_id": str(self.to_account_id) if self.to_account_id else None,
            "transaction_date": self.transaction_date.isoformat(),
            "receipt_url": self.receipt_url,
            "tags": tags,
            "location": self.location,
            "latitude": float(self.latitude) if self.latitude else None,
            "longitude": float(self.longitude) if self.longitude else None,
            "ai_category_confidence": float(self.ai_category_confidence) if self.ai_category_confidence else None,
            "ai_extracted_data": ai_data,
            "sync_id": self.sync_id,
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "sync_status": self.sync_status,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

    def update_tags(self, tags: list):
        """更新标签"""
        self.tags = json.dumps(tags)

    def add_tag(self, tag: str):
        """添加标签"""
        try:
            current_tags = json.loads(self.tags) if self.tags else []
        except (json.JSONDecodeError, TypeError):
            current_tags = []
            
        if tag not in current_tags:
            current_tags.append(tag)
            self.tags = json.dumps(current_tags)

    def remove_tag(self, tag: str):
        """移除标签"""
        try:
            current_tags = json.loads(self.tags) if self.tags else []
        except (json.JSONDecodeError, TypeError):
            current_tags = []
            
        if tag in current_tags:
            current_tags.remove(tag)
            self.tags = json.dumps(current_tags)

    def set_ai_data(self, data: dict, confidence: float = None):
        """设置AI提取的数据"""
        from decimal import Decimal
        self.ai_extracted_data = json.dumps(data)
        if confidence is not None:
            self.ai_category_confidence = Decimal(str(confidence))

    def is_transfer(self) -> bool:
        """检查是否为转账交易"""
        return self.transaction_type == "transfer" and self.to_account_id is not None

    def is_income(self) -> bool:
        """检查是否为收入"""
        return self.transaction_type == "income"

    def is_expense(self) -> bool:
        """检查是否为支出"""
        return self.transaction_type == "expense"
