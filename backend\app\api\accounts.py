from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from ..auth.dependencies import get_current_user
from ..database.connection import get_db
from ..models.user import User
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


# 请求模型
class AccountCreate(BaseModel):
    name: str
    type: str  # cash, bank_card, credit_card, alipay, wechat, other
    balance: float = 0.0
    currency: str = "CNY"
    description: Optional[str] = None
    icon_name: Optional[str] = None
    color: Optional[int] = None


class AccountUpdate(BaseModel):
    name: Optional[str] = None
    balance: Optional[float] = None
    description: Optional[str] = None
    icon_name: Optional[str] = None
    color: Optional[int] = None
    is_active: Optional[bool] = None


# 响应模型
class AccountResponse(BaseModel):
    id: str
    name: str
    type: str
    balance: float
    currency: str
    description: Optional[str]
    icon_name: Optional[str]
    color: Optional[int]
    is_active: bool
    created_at: datetime
    updated_at: datetime


@router.get("/", response_model=List[AccountResponse])
async def get_accounts(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户所有账户"""
    # TODO: 实现获取账户列表
    return []


@router.post("/", response_model=AccountResponse)
async def create_account(
    account_data: AccountCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新账户"""
    # TODO: 实现创建账户
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定账户"""
    # TODO: 实现获取单个账户
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.put("/{account_id}", response_model=AccountResponse)
async def update_account(
    account_id: str,
    account_data: AccountUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新账户信息"""
    # TODO: 实现更新账户
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.delete("/{account_id}")
async def delete_account(
    account_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除账户"""
    # TODO: 实现删除账户
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )
