# 应用配置
APP_NAME=AI记账应用
DEBUG=true
HOST=0.0.0.0
PORT=8000

# 安全配置
SECRET_KEY=your-super-secret-key-change-in-production-please
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/ai_accounting
DATABASE_TEST_URL=postgresql://postgres:password@localhost:5432/ai_accounting_test

# Redis配置
REDIS_URL=redis://localhost:6379/0

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]
ALLOWED_HOSTS=["*"]

# OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-3.5-turbo

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=["image/jpeg","image/png","image/jpg"]

# OCR配置
TESSERACT_CMD=/usr/bin/tesseract

# 语音识别配置
SPEECH_RECOGNITION_TIMEOUT=10
SPEECH_RECOGNITION_LANGUAGE=zh-CN

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Celery配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
