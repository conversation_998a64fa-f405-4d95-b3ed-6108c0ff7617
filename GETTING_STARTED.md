# 🚀 快速开始指南

本指南将帮助您快速设置和运行AI记账应用的开发环境。

## 📋 前置要求

### 必需软件
- **Python 3.9+** - 后端开发
- **Flutter 3.0+** - 前端开发
- **Git** - 版本控制

### 可选软件
- **Docker & Docker Compose** - 容器化部署
- **PostgreSQL** - 生产数据库
- **Redis** - 缓存服务
- **VS Code** - 推荐IDE

## 🛠️ 后端设置

### 1. 环境准备

```bash
# 克隆项目（如果还没有）
git clone <repository-url>
cd 记账报销软件/backend

# 创建虚拟环境（推荐）
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

### 2. 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 如果遇到依赖问题，可以单独安装关键包
pip install fastapi uvicorn sqlalchemy asyncpg
pip install python-jose[cryptography] passlib[bcrypt]
pip install pydantic[email] redis
```

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，配置以下关键设置：
# - SECRET_KEY: 生产环境请使用强密钥
# - DATABASE_URL: 数据库连接字符串
# - REDIS_URL: Redis连接字符串
# - OPENAI_API_KEY: OpenAI API密钥（可选）
```

### 4. 测试设置

```bash
# 运行设置测试
python test_setup.py

# 如果所有测试通过，说明环境配置正确
```

### 5. 启动开发服务器

```bash
# 使用便捷脚本启动
python run_dev.py

# 或者直接使用uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 6. 验证后端服务

访问以下URL验证服务是否正常：
- **健康检查**: http://localhost:8000/health
- **API文档**: http://localhost:8000/docs
- **根路径**: http://localhost:8000/

## 📱 前端设置

### 1. 环境准备

```bash
# 进入前端目录
cd ../ai_accounting_app

# 检查Flutter环境
flutter doctor
```

### 2. 安装依赖

```bash
# 获取Flutter依赖
flutter pub get

# 生成代码（如果需要）
flutter packages pub run build_runner build
```

### 3. 配置API端点

编辑 `lib/constants/app_constants.dart`，确保API_BASE_URL指向正确的后端地址：

```dart
static const String API_BASE_URL = 'http://localhost:8000/api/v1';
```

### 4. 运行应用

```bash
# 启动Flutter应用
flutter run

# 或者指定设备
flutter run -d chrome    # 在Chrome中运行
flutter run -d android   # 在Android设备/模拟器中运行
```

## 🐳 Docker部署（可选）

如果您想使用Docker进行开发或部署：

### 1. 启动服务

```bash
cd backend
docker-compose up -d
```

### 2. 查看服务状态

```bash
docker-compose ps
docker-compose logs -f api
```

### 3. 停止服务

```bash
docker-compose down
```

## 🔧 常见问题解决

### Python依赖问题

```bash
# 如果遇到bcrypt版本警告，可以忽略，不影响功能
# 如果遇到psycopg2问题，安装二进制版本
pip install psycopg2-binary

# 如果遇到jose模块问题
pip install python-jose[cryptography]
```

### Flutter问题

```bash
# 清理Flutter缓存
flutter clean
flutter pub get

# 如果遇到build_runner问题
flutter packages pub run build_runner clean
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### 数据库连接问题

1. 确保PostgreSQL服务正在运行
2. 检查.env文件中的DATABASE_URL配置
3. 对于开发环境，可以使用SQLite：
   ```
   DATABASE_URL=sqlite:///./app.db
   ```

## 📊 开发工具

### API测试

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Postman**: 导入API文档进行测试

### 数据库管理

- **pgAdmin**: PostgreSQL图形化管理工具
- **Redis Commander**: Redis可视化工具

### 代码质量

```bash
# Python代码格式化
black app/
isort app/

# Flutter代码格式化
flutter format lib/
```

## 🎯 下一步

1. **熟悉项目结构** - 查看各个模块的代码组织
2. **阅读API文档** - 了解可用的API端点
3. **运行测试** - 确保功能正常工作
4. **开始开发** - 根据任务列表开始功能开发

## 📞 获取帮助

如果遇到问题：
1. 检查本指南的常见问题部分
2. 查看项目的README.md文件
3. 运行`python test_setup.py`检查环境配置
4. 创建Issue描述具体问题

---

**提示**: 开发过程中建议保持后端服务运行，这样前端可以实时与API交互进行测试。
