# 项目结构详细设计

## 整体架构

```
ai-accounting-app/
├── frontend/                    # Flutter移动端应用
│   ├── android/                # Android平台配置
│   ├── lib/                    # Dart源代码
│   │   ├── main.dart          # 应用入口
│   │   ├── models/            # 数据模型
│   │   │   ├── user.dart
│   │   │   ├── transaction.dart
│   │   │   ├── account.dart
│   │   │   ├── budget.dart
│   │   │   └── category.dart
│   │   ├── services/          # 业务逻辑服务
│   │   │   ├── auth_service.dart
│   │   │   ├── database_service.dart
│   │   │   ├── sync_service.dart
│   │   │   ├── ai_service.dart
│   │   │   ├── voice_service.dart
│   │   │   └── ocr_service.dart
│   │   ├── providers/         # 状态管理
│   │   │   ├── auth_provider.dart
│   │   │   ├── transaction_provider.dart
│   │   │   ├── budget_provider.dart
│   │   │   └── settings_provider.dart
│   │   ├── screens/           # 页面UI
│   │   │   ├── auth/          # 认证相关页面
│   │   │   ├── home/          # 主页
│   │   │   ├── transactions/  # 交易记录
│   │   │   ├── budget/        # 预算管理
│   │   │   ├── reports/       # 报表分析
│   │   │   └── settings/      # 设置页面
│   │   ├── widgets/           # 通用组件
│   │   │   ├── common/        # 通用组件
│   │   │   ├── charts/        # 图表组件
│   │   │   └── forms/         # 表单组件
│   │   ├── utils/             # 工具类
│   │   │   ├── constants.dart
│   │   │   ├── helpers.dart
│   │   │   └── validators.dart
│   │   └── database/          # 本地数据库
│   │       ├── database_helper.dart
│   │       └── migrations/
│   ├── assets/                # 资源文件
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── test/                  # 测试文件
│   ├── pubspec.yaml          # Flutter依赖配置
│   └── analysis_options.yaml # 代码分析配置
│
├── backend/                   # Python后端API
│   ├── app/
│   │   ├── main.py           # FastAPI应用入口
│   │   ├── config.py         # 配置文件
│   │   ├── models/           # SQLAlchemy数据库模型
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── transaction.py
│   │   │   ├── account.py
│   │   │   ├── budget.py
│   │   │   └── category.py
│   │   ├── schemas/          # Pydantic请求/响应模型
│   │   │   ├── __init__.py
│   │   │   ├── user.py
│   │   │   ├── transaction.py
│   │   │   ├── account.py
│   │   │   └── auth.py
│   │   ├── api/              # API路由
│   │   │   ├── __init__.py
│   │   │   ├── auth.py       # 认证API
│   │   │   ├── transactions.py # 交易API
│   │   │   ├── accounts.py   # 账户API
│   │   │   ├── budgets.py    # 预算API
│   │   │   ├── reports.py    # 报表API
│   │   │   └── ai.py         # AI功能API
│   │   ├── services/         # 业务逻辑服务
│   │   │   ├── __init__.py
│   │   │   ├── auth_service.py
│   │   │   ├── transaction_service.py
│   │   │   ├── ai_service.py
│   │   │   ├── ocr_service.py
│   │   │   └── email_service.py
│   │   ├── auth/             # 认证相关
│   │   │   ├── __init__.py
│   │   │   ├── jwt_handler.py
│   │   │   ├── oauth.py
│   │   │   └── dependencies.py
│   │   ├── ai/               # AI功能模块
│   │   │   ├── __init__.py
│   │   │   ├── classifier.py # 交易分类
│   │   │   ├── analyzer.py   # 消费分析
│   │   │   └── recommender.py # 建议系统
│   │   ├── database/         # 数据库配置
│   │   │   ├── __init__.py
│   │   │   ├── connection.py
│   │   │   └── migrations/
│   │   └── utils/            # 工具函数
│   │       ├── __init__.py
│   │       ├── helpers.py
│   │       └── validators.py
│   ├── requirements.txt      # Python依赖
│   ├── Dockerfile           # Docker配置
│   ├── docker-compose.yml   # Docker Compose配置
│   └── alembic.ini          # 数据库迁移配置
│
├── docs/                     # 项目文档
│   ├── api.md               # API文档
│   ├── database.md          # 数据库设计
│   ├── deployment.md        # 部署指南
│   └── development.md       # 开发指南
│
├── scripts/                  # 脚本文件
│   ├── setup.sh            # 环境设置脚本
│   ├── deploy.sh           # 部署脚本
│   └── backup.sh           # 数据备份脚本
│
├── .gitignore              # Git忽略文件
├── README.md               # 项目说明
└── LICENSE                 # 许可证
```

## 数据库设计

### 主要表结构

1. **users** - 用户表
2. **accounts** - 账户表（银行卡、现金、支付宝等）
3. **categories** - 分类表（收入/支出分类）
4. **transactions** - 交易记录表
5. **budgets** - 预算表
6. **sync_logs** - 同步日志表

## API设计

### 认证相关
- POST /auth/login - 用户登录
- POST /auth/register - 用户注册
- POST /auth/refresh - 刷新token
- POST /auth/logout - 用户登出

### 交易相关
- GET /transactions - 获取交易列表
- POST /transactions - 创建交易
- PUT /transactions/{id} - 更新交易
- DELETE /transactions/{id} - 删除交易

### AI功能
- POST /ai/classify - 智能分类
- POST /ai/analyze - 消费分析
- POST /ai/ocr - OCR识别

## 开发优先级

1. 基础架构搭建
2. 用户认证系统
3. 核心记账功能
4. 本地数据存储
5. AI智能分类
6. 数据同步
7. 高级功能（预算、报表等）
