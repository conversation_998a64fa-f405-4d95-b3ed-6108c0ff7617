from sqlalchemy import Column, String, Numeric, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
import json

from ..database.base import Base


class Account(Base):
    """账户模型"""
    __tablename__ = "accounts"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    name = Column(String(100), nullable=False)
    account_type = Column(String(50), nullable=False)  # cash, bank, credit_card, investment, etc.
    
    # 账户详情
    balance = Column(Numeric(15, 2), default=0.00)
    currency = Column(String(3), default="CNY")
    description = Column(Text)
    
    # 银行信息（可选）
    bank_name = Column(String(100))
    account_number = Column(String(50))
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_default = Column(Boolean, default=False)
    
    # 同步相关
    sync_id = Column(String(100), unique=True)
    last_sync = Column(DateTime(timezone=True))
    sync_status = Column(String(20), default="pending")  # pending, synced, conflict
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系（暂时注释掉）
    # user = relationship("User", back_populates="accounts")
    # transactions = relationship("Transaction", back_populates="account", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Account(id={self.id}, name={self.name}, balance={self.balance})>"

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "name": self.name,
            "account_type": self.account_type,
            "balance": float(self.balance),
            "currency": self.currency,
            "description": self.description,
            "bank_name": self.bank_name,
            "account_number": self.account_number,
            "is_active": self.is_active,
            "is_default": self.is_default,
            "sync_id": self.sync_id,
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "sync_status": self.sync_status,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
        }

    def update_balance(self, amount: float):
        """更新账户余额"""
        from decimal import Decimal
        self.balance += Decimal(str(amount))

    def set_as_default(self):
        """设置为默认账户"""
        self.is_default = True

    def deactivate(self):
        """停用账户"""
        self.is_active = False

    def activate(self):
        """激活账户"""
        self.is_active = True

    @classmethod
    def create_default_accounts(cls, user_id: str):
        """为新用户创建默认账户"""
        accounts = [
            cls(
                user_id=user_id,
                name="现金",
                account_type="cash",
                is_default=True,
                sync_id=str(uuid.uuid4()),
            ),
            cls(
                user_id=user_id,
                name="银行卡",
                account_type="bank",
                sync_id=str(uuid.uuid4()),
            ),
        ]
        return accounts
