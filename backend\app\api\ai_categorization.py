from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from typing import List, Optional
from pydantic import BaseModel, Field
from datetime import datetime, timedelta

from ..database.connection import get_db
from ..models.user import User
from ..models.user_ai_config import UserAIConfig
from ..models.category import Category
from ..models.transaction import Transaction
from ..auth.dependencies import get_current_user
from ..services.ai_config import AIProvider, AIModelConfig
from ..services.ai_categorization import TransactionCategorizationService

router = APIRouter(prefix="/ai-categorization", tags=["AI智能分类"])


# Pydantic模型
class TransactionCategorizationRequest(BaseModel):
    """交易分类请求"""
    description: str = Field(..., min_length=1, max_length=500)
    amount: float = Field(..., gt=0)
    transaction_type: str = Field(..., pattern="^(income|expense|transfer)$")
    config_id: Optional[str] = None  # 如果不指定，使用默认配置


class BatchCategorizationRequest(BaseModel):
    """批量分类请求"""
    transactions: List[TransactionCategorizationRequest]
    config_id: Optional[str] = None


class SpendingAnalysisRequest(BaseModel):
    """消费分析请求"""
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    time_period: str = Field(default="month", pattern="^(week|month|quarter|year)$")
    config_id: Optional[str] = None


async def get_user_ai_config(
    user_id: str,
    config_id: Optional[str],
    db: AsyncSession
) -> UserAIConfig:
    """获取用户AI配置"""
    if config_id:
        # 使用指定配置
        result = await db.execute(
            select(UserAIConfig).where(
                and_(
                    UserAIConfig.id == config_id,
                    UserAIConfig.user_id == user_id,
                    UserAIConfig.is_active == True
                )
            )
        )
    else:
        # 使用默认配置
        result = await db.execute(
            select(UserAIConfig).where(
                and_(
                    UserAIConfig.user_id == user_id,
                    UserAIConfig.is_default == True,
                    UserAIConfig.is_active == True
                )
            )
        )
    
    config = result.scalar_one_or_none()
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="未找到可用的AI配置，请先配置AI服务"
        )
    
    return config


async def get_user_categories(user_id: str, transaction_type: str, db: AsyncSession) -> List[dict]:
    """获取用户分类"""
    result = await db.execute(
        select(Category).where(
            and_(
                Category.user_id == user_id,
                Category.category_type == transaction_type,
                Category.is_active == True
            )
        )
    )
    categories = result.scalars().all()
    return [cat.to_dict() for cat in categories]


async def get_user_transaction_history(user_id: str, limit: int, db: AsyncSession) -> List[dict]:
    """获取用户交易历史"""
    result = await db.execute(
        select(Transaction)
        .where(Transaction.user_id == user_id)
        .order_by(Transaction.created_at.desc())
        .limit(limit)
    )
    transactions = result.scalars().all()
    
    history = []
    for trans in transactions:
        # 获取分类名称
        if trans.category_id:
            cat_result = await db.execute(
                select(Category).where(Category.id == trans.category_id)
            )
            category = cat_result.scalar_one_or_none()
            category_name = category.name if category else "未分类"
        else:
            category_name = "未分类"
        
        history.append({
            "description": trans.description,
            "amount": float(trans.amount),
            "category_name": category_name,
            "transaction_type": trans.transaction_type
        })
    
    return history


@router.post("/categorize")
async def categorize_transaction(
    request: TransactionCategorizationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """对单个交易进行AI分类"""
    try:
        # 获取AI配置
        ai_config_model = await get_user_ai_config(str(current_user.id), request.config_id, db)
        
        # 获取用户分类
        categories = await get_user_categories(str(current_user.id), request.transaction_type, db)
        if not categories:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"用户没有可用的{request.transaction_type}分类"
            )
        
        # 获取用户历史记录
        history = await get_user_transaction_history(str(current_user.id), 10, db)
        
        # 创建AI配置
        ai_config = AIModelConfig(
            provider=AIProvider(ai_config_model.provider),
            model_name=ai_config_model.model_name,
            api_key=ai_config_model.api_key,
            api_base=ai_config_model.api_base,
            api_version=ai_config_model.api_version,
            max_tokens=int(ai_config_model.max_tokens),
            temperature=float(ai_config_model.temperature),
            timeout=int(ai_config_model.timeout),
            extra_config=ai_config_model.get_extra_config()
        )
        
        # 创建分类服务
        categorization_service = TransactionCategorizationService(ai_config)
        await categorization_service.initialize()
        
        try:
            # 执行分类
            result = await categorization_service.categorize_transaction(
                transaction_description=request.description,
                amount=request.amount,
                transaction_type=request.transaction_type,
                available_categories=categories,
                user_history=history
            )
            
            # 更新使用统计
            ai_config_model.increment_request_count(success=True)
            await db.commit()
            
            return JSONResponse(content={
                "success": True,
                "result": result,
                "config_used": {
                    "id": str(ai_config_model.id),
                    "name": ai_config_model.config_name,
                    "provider": ai_config_model.provider,
                    "model": ai_config_model.model_name
                }
            })
            
        finally:
            await categorization_service.close()
            
    except HTTPException:
        raise
    except Exception as e:
        # 更新失败统计
        try:
            ai_config_model.increment_request_count(success=False)
            await db.commit()
        except:
            pass
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI分类失败: {str(e)}"
        )


@router.post("/categorize/batch")
async def categorize_transactions_batch(
    request: BatchCategorizationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量对交易进行AI分类"""
    try:
        if len(request.transactions) > 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="批量分类最多支持50个交易"
            )
        
        # 获取AI配置
        ai_config_model = await get_user_ai_config(str(current_user.id), request.config_id, db)
        
        # 获取用户历史记录
        history = await get_user_transaction_history(str(current_user.id), 20, db)
        
        # 创建AI配置
        ai_config = AIModelConfig(
            provider=AIProvider(ai_config_model.provider),
            model_name=ai_config_model.model_name,
            api_key=ai_config_model.api_key,
            api_base=ai_config_model.api_base,
            api_version=ai_config_model.api_version,
            max_tokens=int(ai_config_model.max_tokens),
            temperature=float(ai_config_model.temperature),
            timeout=int(ai_config_model.timeout),
            extra_config=ai_config_model.get_extra_config()
        )
        
        # 创建分类服务
        categorization_service = TransactionCategorizationService(ai_config)
        await categorization_service.initialize()
        
        results = []
        successful_count = 0
        failed_count = 0
        
        try:
            for i, trans_request in enumerate(request.transactions):
                try:
                    # 获取对应类型的分类
                    categories = await get_user_categories(
                        str(current_user.id), 
                        trans_request.transaction_type, 
                        db
                    )
                    
                    if not categories:
                        results.append({
                            "index": i,
                            "success": False,
                            "error": f"没有可用的{trans_request.transaction_type}分类",
                            "result": None
                        })
                        failed_count += 1
                        continue
                    
                    # 执行分类
                    result = await categorization_service.categorize_transaction(
                        transaction_description=trans_request.description,
                        amount=trans_request.amount,
                        transaction_type=trans_request.transaction_type,
                        available_categories=categories,
                        user_history=history
                    )
                    
                    results.append({
                        "index": i,
                        "success": True,
                        "error": None,
                        "result": result
                    })
                    successful_count += 1
                    
                except Exception as e:
                    results.append({
                        "index": i,
                        "success": False,
                        "error": str(e),
                        "result": None
                    })
                    failed_count += 1
            
            # 更新使用统计
            for _ in range(successful_count):
                ai_config_model.increment_request_count(success=True)
            for _ in range(failed_count):
                ai_config_model.increment_request_count(success=False)
            
            await db.commit()
            
            return JSONResponse(content={
                "success": True,
                "results": results,
                "summary": {
                    "total": len(request.transactions),
                    "successful": successful_count,
                    "failed": failed_count
                },
                "config_used": {
                    "id": str(ai_config_model.id),
                    "name": ai_config_model.config_name,
                    "provider": ai_config_model.provider,
                    "model": ai_config_model.model_name
                }
            })
            
        finally:
            await categorization_service.close()
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量AI分类失败: {str(e)}"
        )


@router.post("/analyze/spending")
async def analyze_spending_pattern(
    request: SpendingAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """分析用户消费模式"""
    try:
        # 获取AI配置
        ai_config_model = await get_user_ai_config(str(current_user.id), request.config_id, db)
        
        # 确定时间范围
        end_date = request.end_date or datetime.utcnow()
        if request.start_date:
            start_date = request.start_date
        else:
            # 根据时间周期确定开始时间
            if request.time_period == "week":
                start_date = end_date - timedelta(weeks=1)
            elif request.time_period == "month":
                start_date = end_date - timedelta(days=30)
            elif request.time_period == "quarter":
                start_date = end_date - timedelta(days=90)
            else:  # year
                start_date = end_date - timedelta(days=365)
        
        # 获取时间范围内的交易记录
        result = await db.execute(
            select(Transaction).where(
                and_(
                    Transaction.user_id == str(current_user.id),
                    Transaction.transaction_date >= start_date,
                    Transaction.transaction_date <= end_date
                )
            ).order_by(Transaction.transaction_date.desc())
        )
        transactions = result.scalars().all()
        
        if not transactions:
            return JSONResponse(content={
                "success": True,
                "message": "指定时间范围内没有交易记录",
                "analysis": {
                    "spending_pattern": {},
                    "anomaly_detection": {},
                    "trends": {},
                    "financial_health": {"score": 0, "assessment": "无数据"},
                    "recommendations": []
                }
            })
        
        # 构建交易数据
        transaction_data = []
        for trans in transactions:
            # 获取分类名称
            category_name = "未分类"
            if trans.category_id:
                cat_result = await db.execute(
                    select(Category).where(Category.id == trans.category_id)
                )
                category = cat_result.scalar_one_or_none()
                if category:
                    category_name = category.name
            
            transaction_data.append({
                "description": trans.description,
                "amount": float(trans.amount),
                "category_name": category_name,
                "transaction_type": trans.transaction_type,
                "date": trans.transaction_date.isoformat()
            })
        
        # 创建AI配置
        ai_config = AIModelConfig(
            provider=AIProvider(ai_config_model.provider),
            model_name=ai_config_model.model_name,
            api_key=ai_config_model.api_key,
            api_base=ai_config_model.api_base,
            api_version=ai_config_model.api_version,
            max_tokens=int(ai_config_model.max_tokens),
            temperature=float(ai_config_model.temperature),
            timeout=int(ai_config_model.timeout),
            extra_config=ai_config_model.get_extra_config()
        )
        
        # 创建分类服务
        categorization_service = TransactionCategorizationService(ai_config)
        await categorization_service.initialize()
        
        try:
            # 执行消费模式分析
            analysis_result = await categorization_service.analyze_spending_pattern(
                transactions=transaction_data,
                time_period=request.time_period
            )
            
            # 更新使用统计
            ai_config_model.increment_request_count(success=True)
            await db.commit()
            
            return JSONResponse(content={
                "success": True,
                "analysis": analysis_result,
                "time_range": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "period": request.time_period
                },
                "transaction_count": len(transactions),
                "config_used": {
                    "id": str(ai_config_model.id),
                    "name": ai_config_model.config_name,
                    "provider": ai_config_model.provider,
                    "model": ai_config_model.model_name
                }
            })
            
        finally:
            await categorization_service.close()
            
    except HTTPException:
        raise
    except Exception as e:
        # 更新失败统计
        try:
            ai_config_model.increment_request_count(success=False)
            await db.commit()
        except:
            pass
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"消费模式分析失败: {str(e)}"
        )
