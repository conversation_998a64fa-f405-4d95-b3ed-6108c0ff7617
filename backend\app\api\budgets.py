from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

from ..auth.dependencies import get_current_user
from ..database.connection import get_db
from ..models.user import User
from sqlalchemy.ext.asyncio import AsyncSession

router = APIRouter()


# 请求模型
class BudgetCreate(BaseModel):
    category_id: Optional[str] = None
    name: str
    amount: float
    period: str  # daily, weekly, monthly, yearly
    start_date: datetime
    end_date: datetime


class BudgetUpdate(BaseModel):
    name: Optional[str] = None
    amount: Optional[float] = None
    period: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_active: Optional[bool] = None


# 响应模型
class BudgetResponse(BaseModel):
    id: str
    category_id: Optional[str]
    name: str
    amount: float
    period: str
    start_date: datetime
    end_date: datetime
    is_active: bool
    spent_amount: float  # 已花费金额
    remaining_amount: float  # 剩余金额
    progress_percentage: float  # 进度百分比
    created_at: datetime
    updated_at: datetime


@router.get("/", response_model=List[BudgetResponse])
async def get_budgets(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户所有预算"""
    # TODO: 实现获取预算列表
    return []


@router.post("/", response_model=BudgetResponse)
async def create_budget(
    budget_data: BudgetCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新预算"""
    # TODO: 实现创建预算
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.get("/{budget_id}", response_model=BudgetResponse)
async def get_budget(
    budget_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取指定预算"""
    # TODO: 实现获取单个预算
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.put("/{budget_id}", response_model=BudgetResponse)
async def update_budget(
    budget_id: str,
    budget_data: BudgetUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新预算"""
    # TODO: 实现更新预算
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )


@router.delete("/{budget_id}")
async def delete_budget(
    budget_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除预算"""
    # TODO: 实现删除预算
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="功能正在开发中"
    )
