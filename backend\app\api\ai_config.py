from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from typing import List, Optional
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

from ..database.connection import get_db
from ..models.user_ai_config import UserAIConfig, AIUsageLog
from ..models.user import User
from ..auth.dependencies import get_current_user
from ..services.ai_config import AIProvider, AIModelConfig, DefaultAIConfigs, AIConfigValidator
from ..services.ai_client import AIClientFactory

router = APIRouter(prefix="/ai-config", tags=["AI配置管理"])


# Pydantic模型
class AIConfigCreate(BaseModel):
    """创建AI配置请求"""
    provider: AIProvider
    model_name: str
    api_key: str
    api_base: Optional[str] = None
    api_version: Optional[str] = None
    max_tokens: int = Field(default=1000, ge=1, le=4000)
    temperature: float = Field(default=0.3, ge=0.0, le=2.0)
    timeout: int = Field(default=30, ge=5, le=120)
    extra_config: dict = Field(default_factory=dict)
    config_name: str
    description: Optional[str] = None
    is_default: bool = False


class AIConfigUpdate(BaseModel):
    """更新AI配置请求"""
    model_name: Optional[str] = None
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    api_version: Optional[str] = None
    max_tokens: Optional[int] = Field(None, ge=1, le=4000)
    temperature: Optional[float] = Field(None, ge=0.0, le=2.0)
    timeout: Optional[int] = Field(None, ge=5, le=120)
    extra_config: Optional[dict] = None
    config_name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None


class AIConfigTest(BaseModel):
    """测试AI配置请求"""
    test_message: str = "你好，这是一个测试消息。"


@router.get("/providers")
async def get_ai_providers():
    """获取支持的AI提供商列表"""
    try:
        providers_info = DefaultAIConfigs.get_provider_info()
        default_configs = DefaultAIConfigs.get_default_configs()
        
        result = []
        for provider, info in providers_info.items():
            config = default_configs.get(provider, {})
            result.append({
                "provider": provider,
                "name": info["name"],
                "description": info["description"],
                "website": info["website"],
                "docs": info["docs"],
                "models": config.get("models", []),
                "default_model": config.get("default_model", ""),
                "supported": provider in AIClientFactory.get_supported_providers()
            })
        
        return JSONResponse(content={
            "providers": result,
            "total": len(result)
        })
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI提供商列表失败: {str(e)}"
        )


@router.get("/configs")
async def get_user_ai_configs(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户的AI配置列表"""
    try:
        result = await db.execute(
            select(UserAIConfig).where(UserAIConfig.user_id == str(current_user.id))
        )
        configs = result.scalars().all()
        
        return JSONResponse(content={
            "configs": [config.to_dict() for config in configs],
            "total": len(configs)
        })
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI配置失败: {str(e)}"
        )


@router.post("/configs")
async def create_ai_config(
    config_data: AIConfigCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建AI配置"""
    try:
        # 验证配置
        ai_config = AIModelConfig(
            provider=config_data.provider,
            model_name=config_data.model_name,
            api_key=config_data.api_key,
            api_base=config_data.api_base,
            api_version=config_data.api_version,
            max_tokens=config_data.max_tokens,
            temperature=config_data.temperature,
            timeout=config_data.timeout,
            extra_config=config_data.extra_config
        )
        
        is_valid, error_msg = AIConfigValidator.validate_config(ai_config)
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"配置验证失败: {error_msg}"
            )
        
        # 如果设置为默认配置，先取消其他默认配置
        if config_data.is_default:
            await db.execute(
                update(UserAIConfig)
                .where(UserAIConfig.user_id == str(current_user.id))
                .values(is_default=False)
            )
        
        # 创建新配置
        new_config = UserAIConfig(
            user_id=str(current_user.id),
            provider=config_data.provider.value,
            model_name=config_data.model_name,
            api_key=config_data.api_key,  # 在实际应用中应该加密存储
            api_base=config_data.api_base,
            api_version=config_data.api_version,
            max_tokens=config_data.max_tokens,
            temperature=config_data.temperature,
            timeout=config_data.timeout,
            config_name=config_data.config_name,
            description=config_data.description,
            is_default=config_data.is_default,
        )
        
        new_config.update_extra_config(config_data.extra_config)
        
        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)
        
        return JSONResponse(content={
            "message": "AI配置创建成功",
            "config": new_config.to_dict()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建AI配置失败: {str(e)}"
        )


@router.put("/configs/{config_id}")
async def update_ai_config(
    config_id: str,
    config_data: AIConfigUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新AI配置"""
    try:
        # 查找配置
        result = await db.execute(
            select(UserAIConfig).where(
                UserAIConfig.id == config_id,
                UserAIConfig.user_id == str(current_user.id)
            )
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI配置不存在"
            )
        
        # 如果设置为默认配置，先取消其他默认配置
        if config_data.is_default:
            await db.execute(
                update(UserAIConfig)
                .where(
                    UserAIConfig.user_id == str(current_user.id),
                    UserAIConfig.id != config_id
                )
                .values(is_default=False)
            )
        
        # 更新配置
        update_data = {}
        for field, value in config_data.dict(exclude_unset=True).items():
            if field == "extra_config" and value is not None:
                config.update_extra_config(value)
            else:
                update_data[field] = value
        
        if update_data:
            await db.execute(
                update(UserAIConfig)
                .where(UserAIConfig.id == config_id)
                .values(**update_data)
            )
        
        await db.commit()
        await db.refresh(config)
        
        return JSONResponse(content={
            "message": "AI配置更新成功",
            "config": config.to_dict()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI配置失败: {str(e)}"
        )


@router.delete("/configs/{config_id}")
async def delete_ai_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除AI配置"""
    try:
        # 查找配置
        result = await db.execute(
            select(UserAIConfig).where(
                UserAIConfig.id == config_id,
                UserAIConfig.user_id == str(current_user.id)
            )
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI配置不存在"
            )
        
        # 删除配置
        await db.execute(
            delete(UserAIConfig).where(UserAIConfig.id == config_id)
        )
        
        await db.commit()
        
        return JSONResponse(content={
            "message": "AI配置删除成功"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除AI配置失败: {str(e)}"
        )


@router.post("/configs/{config_id}/test")
async def test_ai_config(
    config_id: str,
    test_data: AIConfigTest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """测试AI配置"""
    try:
        # 查找配置
        result = await db.execute(
            select(UserAIConfig).where(
                UserAIConfig.id == config_id,
                UserAIConfig.user_id == str(current_user.id)
            )
        )
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI配置不存在"
            )
        
        # 创建AI客户端并测试
        ai_config = AIModelConfig(
            provider=AIProvider(config.provider),
            model_name=config.model_name,
            api_key=config.api_key,
            api_base=config.api_base,
            api_version=config.api_version,
            max_tokens=int(config.max_tokens),
            temperature=float(config.temperature),
            timeout=int(config.timeout),
            extra_config=config.get_extra_config()
        )
        
        client = AIClientFactory.create_client(ai_config)
        
        try:
            start_time = datetime.utcnow()
            messages = client.format_messages(test_data.test_message, "你是一个AI助手，请简短回复。")
            response = await client.chat_completion(messages)
            end_time = datetime.utcnow()
            
            response_time = (end_time - start_time).total_seconds()
            
            # 记录使用日志
            config.increment_request_count(success=True)
            await db.commit()
            
            return JSONResponse(content={
                "success": True,
                "message": "AI配置测试成功",
                "response": response["choices"][0]["message"]["content"],
                "response_time": response_time,
                "usage": response.get("usage", {})
            })
            
        except Exception as e:
            # 记录失败日志
            config.increment_request_count(success=False)
            await db.commit()
            
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content={
                    "success": False,
                    "message": "AI配置测试失败",
                    "error": str(e)
                }
            )
        finally:
            await client.close()
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试AI配置失败: {str(e)}"
        )
