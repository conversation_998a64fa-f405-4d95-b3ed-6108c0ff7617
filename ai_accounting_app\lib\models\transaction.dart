import 'package:json_annotation/json_annotation.dart';
import '../constants/app_constants.dart';

part 'transaction.g.dart';

@JsonSerializable()
class Transaction {
  final String id;
  final String userId;
  final String accountId;
  final String? toAccountId; // 转账目标账户
  final String categoryId;
  final TransactionType type;
  final double amount;
  final String currency;
  final String? description;
  final String? note;
  final DateTime transactionDate;
  final String? location;
  final List<String>? tags;
  final String? receiptImageUrl;
  final bool isRecurring;
  final String? recurringRule;
  final SyncStatus syncStatus;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? syncId;

  const Transaction({
    required this.id,
    required this.userId,
    required this.accountId,
    this.toAccountId,
    required this.categoryId,
    required this.type,
    required this.amount,
    this.currency = 'CNY',
    this.description,
    this.note,
    required this.transactionDate,
    this.location,
    this.tags,
    this.receiptImageUrl,
    this.isRecurring = false,
    this.recurringRule,
    this.syncStatus = SyncStatus.pending,
    required this.createdAt,
    required this.updatedAt,
    this.syncId,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) => _$TransactionFromJson(json);
  Map<String, dynamic> toJson() => _$TransactionToJson(this);

  // 从数据库记录创建Transaction对象
  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] as String,
      userId: map['user_id'] as String,
      accountId: map['account_id'] as String,
      toAccountId: map['to_account_id'] as String?,
      categoryId: map['category_id'] as String,
      type: TransactionType.values[map['type'] as int],
      amount: (map['amount'] as num).toDouble(),
      currency: map['currency'] as String? ?? 'CNY',
      description: map['description'] as String?,
      note: map['note'] as String?,
      transactionDate: DateTime.parse(map['transaction_date'] as String),
      location: map['location'] as String?,
      tags: map['tags'] != null ? (map['tags'] as String).split(',') : null,
      receiptImageUrl: map['receipt_image_url'] as String?,
      isRecurring: (map['is_recurring'] as int) == 1,
      recurringRule: map['recurring_rule'] as String?,
      syncStatus: SyncStatus.values[map['sync_status'] as int],
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
      syncId: map['sync_id'] as String?,
    );
  }

  // 转换为数据库记录
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'account_id': accountId,
      'to_account_id': toAccountId,
      'category_id': categoryId,
      'type': type.index,
      'amount': amount,
      'currency': currency,
      'description': description,
      'note': note,
      'transaction_date': transactionDate.toIso8601String(),
      'location': location,
      'tags': tags?.join(','),
      'receipt_image_url': receiptImageUrl,
      'is_recurring': isRecurring ? 1 : 0,
      'recurring_rule': recurringRule,
      'sync_status': syncStatus.index,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'sync_id': syncId,
    };
  }

  Transaction copyWith({
    String? id,
    String? userId,
    String? accountId,
    String? toAccountId,
    String? categoryId,
    TransactionType? type,
    double? amount,
    String? currency,
    String? description,
    String? note,
    DateTime? transactionDate,
    String? location,
    List<String>? tags,
    String? receiptImageUrl,
    bool? isRecurring,
    String? recurringRule,
    SyncStatus? syncStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? syncId,
  }) {
    return Transaction(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      accountId: accountId ?? this.accountId,
      toAccountId: toAccountId ?? this.toAccountId,
      categoryId: categoryId ?? this.categoryId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      description: description ?? this.description,
      note: note ?? this.note,
      transactionDate: transactionDate ?? this.transactionDate,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      receiptImageUrl: receiptImageUrl ?? this.receiptImageUrl,
      isRecurring: isRecurring ?? this.isRecurring,
      recurringRule: recurringRule ?? this.recurringRule,
      syncStatus: syncStatus ?? this.syncStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncId: syncId ?? this.syncId,
    );
  }

  // 获取交易类型显示名称
  String get typeDisplayName {
    switch (type) {
      case TransactionType.income:
        return '收入';
      case TransactionType.expense:
        return '支出';
      case TransactionType.transfer:
        return '转账';
    }
  }

  // 获取交易类型颜色
  int get typeColor {
    switch (type) {
      case TransactionType.income:
        return AppColors.incomeColor;
      case TransactionType.expense:
        return AppColors.expenseColor;
      case TransactionType.transfer:
        return AppColors.transferColor;
    }
  }

  // 格式化金额显示
  String get formattedAmount {
    final prefix = type == TransactionType.income ? '+' : 
                  type == TransactionType.expense ? '-' : '';
    return '$prefix¥${amount.toStringAsFixed(2)}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Transaction && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Transaction(id: $id, type: $type, amount: $amount, description: $description)';
  }
}
