from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
import json


class AIProvider(str, Enum):
    """AI服务提供商枚举"""
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    GOOGLE_GEMINI = "google_gemini"
    BAIDU_QIANFAN = "baidu_qianfan"
    ALIBABA_DASHSCOPE = "alibaba_dashscope"
    ZHIPU_AI = "zhipu_ai"
    MOONSHOT = "moonshot"
    DEEPSEEK = "deepseek"
    CUSTOM = "custom"


class AIModelConfig(BaseModel):
    """AI模型配置"""
    provider: AIProvider
    model_name: str
    api_key: str
    api_base: Optional[str] = None
    api_version: Optional[str] = None
    max_tokens: int = Field(default=1000, ge=1, le=4000)
    temperature: float = Field(default=0.3, ge=0.0, le=2.0)
    timeout: int = Field(default=30, ge=5, le=120)
    
    # 特定提供商的额外配置
    extra_config: Dict[str, Any] = Field(default_factory=dict)


class DefaultAIConfigs:
    """默认AI配置"""
    
    @staticmethod
    def get_default_configs() -> Dict[AIProvider, Dict[str, Any]]:
        """获取默认配置"""
        return {
            AIProvider.OPENAI: {
                "api_base": "https://api.openai.com/v1",
                "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
                "default_model": "gpt-3.5-turbo",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.AZURE_OPENAI: {
                "api_base": "https://{resource}.openai.azure.com",
                "api_version": "2024-02-15-preview",
                "models": ["gpt-35-turbo", "gpt-4", "gpt-4-turbo"],
                "default_model": "gpt-35-turbo",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.ANTHROPIC: {
                "api_base": "https://api.anthropic.com",
                "models": ["claude-3-haiku-20240307", "claude-3-sonnet-20240229", "claude-3-opus-20240229"],
                "default_model": "claude-3-haiku-20240307",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.GOOGLE_GEMINI: {
                "api_base": "https://generativelanguage.googleapis.com/v1beta",
                "models": ["gemini-pro", "gemini-pro-vision"],
                "default_model": "gemini-pro",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.BAIDU_QIANFAN: {
                "api_base": "https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat",
                "models": ["ernie-bot", "ernie-bot-turbo", "ernie-bot-4"],
                "default_model": "ernie-bot-turbo",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.ALIBABA_DASHSCOPE: {
                "api_base": "https://dashscope.aliyuncs.com/api/v1",
                "models": ["qwen-turbo", "qwen-plus", "qwen-max"],
                "default_model": "qwen-turbo",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.ZHIPU_AI: {
                "api_base": "https://open.bigmodel.cn/api/paas/v4",
                "models": ["glm-3-turbo", "glm-4", "glm-4v"],
                "default_model": "glm-3-turbo",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.MOONSHOT: {
                "api_base": "https://api.moonshot.cn/v1",
                "models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
                "default_model": "moonshot-v1-8k",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
            AIProvider.DEEPSEEK: {
                "api_base": "https://api.deepseek.com/v1",
                "models": ["deepseek-chat", "deepseek-coder"],
                "default_model": "deepseek-chat",
                "max_tokens": 1000,
                "temperature": 0.3,
            },
        }
    
    @staticmethod
    def get_provider_info() -> Dict[AIProvider, Dict[str, str]]:
        """获取提供商信息"""
        return {
            AIProvider.OPENAI: {
                "name": "OpenAI",
                "description": "OpenAI官方API，支持GPT系列模型",
                "website": "https://openai.com",
                "docs": "https://platform.openai.com/docs",
            },
            AIProvider.AZURE_OPENAI: {
                "name": "Azure OpenAI",
                "description": "微软Azure平台的OpenAI服务",
                "website": "https://azure.microsoft.com/en-us/products/ai-services/openai-service",
                "docs": "https://docs.microsoft.com/en-us/azure/cognitive-services/openai/",
            },
            AIProvider.ANTHROPIC: {
                "name": "Anthropic Claude",
                "description": "Anthropic公司的Claude系列模型",
                "website": "https://www.anthropic.com",
                "docs": "https://docs.anthropic.com",
            },
            AIProvider.GOOGLE_GEMINI: {
                "name": "Google Gemini",
                "description": "Google的Gemini系列模型",
                "website": "https://ai.google.dev",
                "docs": "https://ai.google.dev/docs",
            },
            AIProvider.BAIDU_QIANFAN: {
                "name": "百度千帆",
                "description": "百度智能云千帆大模型平台",
                "website": "https://cloud.baidu.com/product/wenxinworkshop",
                "docs": "https://cloud.baidu.com/doc/WENXINWORKSHOP/index.html",
            },
            AIProvider.ALIBABA_DASHSCOPE: {
                "name": "阿里云灵积",
                "description": "阿里云灵积模型服务平台",
                "website": "https://dashscope.aliyun.com",
                "docs": "https://help.aliyun.com/zh/dashscope/",
            },
            AIProvider.ZHIPU_AI: {
                "name": "智谱AI",
                "description": "智谱AI的GLM系列模型",
                "website": "https://www.zhipuai.cn",
                "docs": "https://open.bigmodel.cn/dev/api",
            },
            AIProvider.MOONSHOT: {
                "name": "月之暗面",
                "description": "月之暗面的Moonshot系列模型",
                "website": "https://www.moonshot.cn",
                "docs": "https://platform.moonshot.cn/docs",
            },
            AIProvider.DEEPSEEK: {
                "name": "DeepSeek",
                "description": "DeepSeek的深度求索模型",
                "website": "https://www.deepseek.com",
                "docs": "https://platform.deepseek.com/api-docs",
            },
        }


class AIConfigValidator:
    """AI配置验证器"""
    
    @staticmethod
    def validate_config(config: AIModelConfig) -> tuple[bool, str]:
        """验证AI配置"""
        try:
            # 基础验证
            if not config.api_key or config.api_key.strip() == "":
                return False, "API密钥不能为空"
            
            if not config.model_name or config.model_name.strip() == "":
                return False, "模型名称不能为空"
            
            # 特定提供商验证
            if config.provider == AIProvider.AZURE_OPENAI:
                if not config.api_base or "{resource}" in config.api_base:
                    return False, "Azure OpenAI需要配置正确的资源端点"
                if not config.api_version:
                    return False, "Azure OpenAI需要配置API版本"
            
            if config.provider == AIProvider.BAIDU_QIANFAN:
                extra_config = config.extra_config
                if not extra_config.get("secret_key"):
                    return False, "百度千帆需要配置Secret Key"
            
            return True, "配置验证通过"
            
        except Exception as e:
            return False, f"配置验证失败: {str(e)}"
    
    @staticmethod
    def get_required_fields(provider: AIProvider) -> Dict[str, str]:
        """获取特定提供商的必需字段"""
        required_fields = {
            "api_key": "API密钥",
            "model_name": "模型名称",
        }
        
        if provider == AIProvider.AZURE_OPENAI:
            required_fields.update({
                "api_base": "Azure资源端点",
                "api_version": "API版本",
            })
        elif provider == AIProvider.BAIDU_QIANFAN:
            required_fields.update({
                "extra_config.secret_key": "Secret Key",
            })
        elif provider == AIProvider.CUSTOM:
            required_fields.update({
                "api_base": "API端点",
            })
        
        return required_fields
