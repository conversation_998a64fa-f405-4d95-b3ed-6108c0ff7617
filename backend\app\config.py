from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """应用配置"""
    
    # 基本配置
    APP_NAME: str = "AI记账应用"
    DEBUG: bool = True
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7
    
    # 数据库配置
    DATABASE_URL: str = "sqlite+aiosqlite:///./ai_accounting.db"
    DATABASE_TEST_URL: str = "sqlite+aiosqlite:///./ai_accounting_test.db"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # CORS配置
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8080",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:8080",
    ]
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # OAuth配置
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    
    # AI服务配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"
    
    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_FILE_SIZE: int = 5 * 1024 * 1024  # 5MB
    ALLOWED_FILE_TYPES: List[str] = ["image/jpeg", "image/png", "image/jpg"]
    
    # OCR配置
    TESSERACT_CMD: Optional[str] = None  # Tesseract可执行文件路径
    
    # 语音识别配置
    SPEECH_RECOGNITION_TIMEOUT: int = 10
    SPEECH_RECOGNITION_LANGUAGE: str = "zh-CN"
    
    # 邮件配置
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USERNAME: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_USE_TLS: bool = True
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # Celery配置（用于异步任务）
    CELERY_BROKER_URL: str = "redis://localhost:6379/1"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/2"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 确保上传目录存在
        upload_path = Path(self.UPLOAD_DIR)
        upload_path.mkdir(parents=True, exist_ok=True)
        
        # 确保日志目录存在
        log_path = Path(self.LOG_FILE).parent
        log_path.mkdir(parents=True, exist_ok=True)


# 创建全局设置实例
settings = Settings()


# 数据库配置类
class DatabaseConfig:
    """数据库相关配置"""
    
    @staticmethod
    def get_database_url(test: bool = False) -> str:
        """获取数据库URL"""
        url = settings.DATABASE_TEST_URL if test else settings.DATABASE_URL
        # 将postgresql://转换为postgresql+asyncpg://以支持异步
        if url.startswith("postgresql://"):
            url = url.replace("postgresql://", "postgresql+asyncpg://", 1)
        # SQLite已经包含了aiosqlite驱动，无需修改
        return url
    
    @staticmethod
    def get_engine_config() -> dict:
        """获取数据库引擎配置"""
        return {
            "pool_size": 10,
            "max_overflow": 20,
            "pool_pre_ping": True,
            "pool_recycle": 3600,
        }


# AI服务配置类
class AIConfig:
    """AI服务相关配置"""
    
    # 交易分类提示词
    TRANSACTION_CLASSIFICATION_PROMPT = """
    请根据以下交易描述，将其分类到合适的类别中。
    
    可选类别：
    收入类别：工资、奖金、投资收益、兼职、其他收入
    支出类别：餐饮、交通、购物、娱乐、医疗、教育、住房、通讯、其他支出
    
    交易描述：{description}
    
    请只返回类别名称，不要包含其他内容。
    """
    
    # 消费分析提示词
    SPENDING_ANALYSIS_PROMPT = """
    请分析用户的消费数据，提供以下分析：
    1. 消费习惯总结
    2. 主要支出类别
    3. 异常消费提醒
    4. 节省建议
    
    消费数据：{transactions_data}
    """
    
    # 预算建议提示词
    BUDGET_SUGGESTION_PROMPT = """
    基于用户的历史消费数据，为以下类别提供合理的预算建议：
    
    历史数据：{historical_data}
    目标类别：{category}
    时间周期：{period}
    
    请提供具体的预算金额和理由。
    """


# 缓存配置
class CacheConfig:
    """缓存相关配置"""
    
    # 缓存键前缀
    USER_CACHE_PREFIX = "user:"
    TRANSACTION_CACHE_PREFIX = "transaction:"
    ACCOUNT_CACHE_PREFIX = "account:"
    
    # 缓存过期时间（秒）
    USER_CACHE_TTL = 3600  # 1小时
    TRANSACTION_CACHE_TTL = 1800  # 30分钟
    ACCOUNT_CACHE_TTL = 3600  # 1小时
    
    # 分页缓存
    PAGINATION_CACHE_TTL = 300  # 5分钟
