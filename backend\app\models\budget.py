from sqlalchemy import Column, String, Numeric, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
import uuid
import json

from ..database.base import Base


class Budget(Base):
    """预算模型"""
    __tablename__ = "budgets"

    # 基本信息
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False, index=True)
    category_id = Column(String(36), ForeignKey("categories.id"), nullable=True, index=True)
    
    # 预算详情
    name = Column(String(100), nullable=False)
    amount = Column(Numeric(15, 2), nullable=False)
    spent_amount = Column(Numeric(15, 2), default=0.00)
    currency = Column(String(3), default="CNY")
    
    # 时间周期
    period_type = Column(String(20), nullable=False)  # monthly, weekly, yearly, custom
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    
    # 提醒设置
    alert_threshold = Column(Numeric(3, 2), default=0.80)  # 80%时提醒
    is_alert_enabled = Column(Boolean, default=True)
    last_alert_sent = Column(DateTime(timezone=True))
    
    # 状态
    is_active = Column(Boolean, default=True)
    is_recurring = Column(Boolean, default=False)  # 是否循环预算
    
    # 同步相关
    sync_id = Column(String(100), unique=True)
    last_sync = Column(DateTime(timezone=True))
    sync_status = Column(String(20), default="pending")  # pending, synced, conflict
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系（暂时注释掉）
    # user = relationship("User", back_populates="budgets")
    # category = relationship("Category", back_populates="budgets")

    def __repr__(self):
        return f"<Budget(id={self.id}, name={self.name}, amount={self.amount})>"

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "category_id": str(self.category_id) if self.category_id else None,
            "name": self.name,
            "amount": float(self.amount),
            "spent_amount": float(self.spent_amount),
            "currency": self.currency,
            "period_type": self.period_type,
            "start_date": self.start_date.isoformat(),
            "end_date": self.end_date.isoformat(),
            "alert_threshold": float(self.alert_threshold),
            "is_alert_enabled": self.is_alert_enabled,
            "last_alert_sent": self.last_alert_sent.isoformat() if self.last_alert_sent else None,
            "is_active": self.is_active,
            "is_recurring": self.is_recurring,
            "sync_id": self.sync_id,
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "sync_status": self.sync_status,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "remaining_amount": float(self.remaining_amount),
            "usage_percentage": float(self.usage_percentage),
            "is_over_budget": self.is_over_budget,
            "days_remaining": self.days_remaining,
        }

    @property
    def remaining_amount(self):
        """剩余预算金额"""
        return self.amount - self.spent_amount

    @property
    def usage_percentage(self):
        """使用百分比"""
        from decimal import Decimal
        if self.amount == 0:
            return Decimal('0')
        return (self.spent_amount / self.amount) * 100

    @property
    def is_over_budget(self) -> bool:
        """是否超出预算"""
        return self.spent_amount > self.amount

    @property
    def days_remaining(self) -> int:
        """剩余天数"""
        now = datetime.utcnow()
        if self.end_date <= now:
            return 0
        return (self.end_date - now).days

    @property
    def should_alert(self) -> bool:
        """是否应该发送提醒"""
        if not self.is_alert_enabled:
            return False
        
        usage_ratio = self.spent_amount / self.amount if self.amount > 0 else 0
        return usage_ratio >= self.alert_threshold

    def add_expense(self, amount):
        """添加支出到预算"""
        self.spent_amount += amount

    def remove_expense(self, amount):
        """从预算中移除支出"""
        from decimal import Decimal
        self.spent_amount = max(Decimal('0'), self.spent_amount - amount)

    def reset_spent_amount(self):
        """重置已花费金额"""
        from decimal import Decimal
        self.spent_amount = Decimal('0')

    def is_current(self) -> bool:
        """检查预算是否在当前时间范围内"""
        now = datetime.utcnow()
        return self.start_date <= now <= self.end_date

    def is_expired(self) -> bool:
        """检查预算是否已过期"""
        return datetime.utcnow() > self.end_date

    def extend_period(self):
        """延长预算周期（用于循环预算）"""
        if not self.is_recurring:
            return
        
        if self.period_type == "monthly":
            # 延长一个月
            if self.end_date.month == 12:
                new_end = self.end_date.replace(year=self.end_date.year + 1, month=1)
            else:
                new_end = self.end_date.replace(month=self.end_date.month + 1)
            
            self.start_date = self.end_date
            self.end_date = new_end
            
        elif self.period_type == "weekly":
            # 延长一周
            self.start_date = self.end_date
            self.end_date = self.end_date + timedelta(weeks=1)
            
        elif self.period_type == "yearly":
            # 延长一年
            self.start_date = self.end_date
            self.end_date = self.end_date.replace(year=self.end_date.year + 1)
        
        # 重置已花费金额
        self.reset_spent_amount()

    @classmethod
    def create_monthly_budget(cls, user_id: str, category_id: str, name: str, amount, start_date: datetime = None):
        """创建月度预算"""
        if start_date is None:
            start_date = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 计算月末
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            end_date = start_date.replace(month=start_date.month + 1, day=1) - timedelta(days=1)
        
        end_date = end_date.replace(hour=23, minute=59, second=59)
        
        return cls(
            user_id=user_id,
            category_id=category_id,
            name=name,
            amount=amount,
            period_type="monthly",
            start_date=start_date,
            end_date=end_date,
            is_recurring=True,
            sync_id=str(uuid.uuid4()),
        )
